sudo cat /var/www/api/fish-api/src/main/java/com/example/fishapi/client/impl/FlaskApiClientImpl.java
package com.example.fishapi.client.impl;

import com.example.fishapi.client.FlaskApiClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

import java.nio.file.Path;
import java.nio.file.Paths;

@Service
@Slf4j
public class FlaskApiClientImpl implements FlaskApiClient {

    private final RestTemplate restTemplate;

    @Value("${flask.api.url:http://127.0.0.1:5000/predict}") // 默认指向本地 Flask API 的 /predict
    private String flaskApiUrl;

    @Autowired
    public FlaskApiClientImpl(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    @Override
    public String predict(String filePath, String fishType) {
        log.info("准备调用 Flask API (单文件): URL={}, filePath={}, fishType={}", flaskApiUrl, filePath, fishType);

        Path path = Paths.get(filePath);
        FileSystemResource fileResource = new FileSystemResource(path);
        if (!fileResource.exists()) {
            log.error("无法调用 Flask API：文件 {} 不存在", filePath);
            throw new RuntimeException("输入文件未找到: " + filePath);
        }

        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", fileResource);
        body.add("fish_type", fishType);
        body.add("excel_engine", "openpyxl"); // 明确指定引擎

        String fileName = path.getFileName().toString().toLowerCase();
        if (fileName.endsWith(".xlsx") || fileName.endsWith(".xls")) {
            log.info("检测到Excel文件类型: {}", fileName);
            body.add("file_type", "excel"); // 可选，如果Python端需要区分
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

        try {
            log.info("向Flask API发送POST请求 (单文件): URL={}, 请求体类型=MultiValueMap", flaskApiUrl);
            ResponseEntity<String> response = restTemplate.postForEntity(flaskApiUrl, requestEntity, String.class);
            // 记录响应（截断可能过长的响应体）
            String responseBody = response.getBody();
            log.info("收到Flask API响应 (单文件): 状态码={}, 响应体长度={}", response.getStatusCode(), responseBody != null ? responseBody.length() : 0);
            if (responseBody != null && log.isDebugEnabled()) { // 仅在 DEBUG 级别记录完整响应体
                 log.debug("完整响应体 (单文件): {}", responseBody.substring(0, Math.min(responseBody.length(), 1000))); // 截断显示
            }


            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Flask API 调用成功 (单文件)，状态码: {}", response.getStatusCode());
                return responseBody;
            } else {
                log.error("Flask API 调用失败 (单文件)，状态码: {}, 响应体: {}", response.getStatusCode(), responseBody);
                throw new RuntimeException("Flask API 返回非成功状态码: " + response.getStatusCode());
            }
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            log.error("调用 Flask API (单文件) 时发生 HTTP 错误: 状态码 {}, 响应体 {}", e.getStatusCode(), e.getResponseBodyAsString(), e);
            throw new RuntimeException("调用 Flask API 失败: " + e.getStatusCode() + " - " + e.getResponseBodyAsString(), e);
        } catch (ResourceAccessException e) {
             log.error("调用 Flask API (单文件) 时发生网络访问错误: {}", e.getMessage(), e);
             throw new RuntimeException("无法连接到 Flask API: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("调用 Flask API (单文件) 时发生未知错误: {}", e.getMessage(), e);
            throw new RuntimeException("调用 Flask API 时发生未知错误: " + e.getMessage(), e);
        }
    }

    @Override
    public String predictMultipleSamples(List<Map<String, Object>> samples, String fishType) {
        log.info("准备调用 Flask API 进行多样本预测: URL={}, 样本数={}, fishType={}",
                 flaskApiUrl, samples.size(), fishType);

        if (samples.isEmpty()) {
            log.error("无法调用 Flask API：样本列表为空");
            throw new RuntimeException("样本列表不能为空");
        }

        try {
            String samplesJson = new ObjectMapper().writeValueAsString(samples);
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("samples_json", samplesJson); // 使用 samples_json 发送数据
            body.add("fish_type", fishType);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA); // 保持 content type
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            log.info("向Flask API发送POST请求 (批量JSON): URL={}, 请求体类型=MultiValueMap", flaskApiUrl);
            ResponseEntity<String> response = restTemplate.postForEntity(
                flaskApiUrl, requestEntity, String.class); // 直接调用 /predict

            // 记录响应（截断可能过长的响应体）
            String responseBody = response.getBody();
            log.info("收到Flask API响应 (批量JSON): 状态码={}, 响应体长度={}", response.getStatusCode(), responseBody != null ? responseBody.length() : 0);
             if (responseBody != null && log.isDebugEnabled()) { // 仅在 DEBUG 级别记录完整响应体
                 log.debug("完整响应体 (批量JSON): {}", responseBody.substring(0, Math.min(responseBody.length(), 1000))); // 截断显示
             }


            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Flask API 批量预测调用成功，状态码: {}", response.getStatusCode());
                return responseBody;
            } else {
                log.error("Flask API 批量预测调用失败，状态码: {}, 响应体: {}",
                          response.getStatusCode(), responseBody);
                throw new RuntimeException("Flask API 返回非成功状态码: " + response.getStatusCode());
            }
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            log.error("调用 Flask API (批量JSON) 时发生 HTTP 错误: 状态码 {}, 响应体 {}", e.getStatusCode(), e.getResponseBodyAsString(), e);
            throw new RuntimeException("调用 Flask API (批量) 失败: " + e.getStatusCode() + " - " + e.getResponseBodyAsString(), e);
        } catch (ResourceAccessException e) {
             log.error("调用 Flask API (批量JSON) 时发生网络访问错误: {}", e.getMessage(), e);
             throw new RuntimeException("无法连接到 Flask API (批量): " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("调用 Flask API (批量JSON) 或处理响应时发生未知错误: {}", e.getMessage(), e);
            throw new RuntimeException("调用 Flask API (批量) 或处理响应时发生未知错误", e);
        }
    }
}
