package com.example.fishapi.config;

import com.example.fishapi.security.JwtAuthenticationFilter;
import com.example.fishapi.security.JwtAuthorizationFilter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true) 
public class ImprovedSecurityConfig {
    private static final Logger log = LoggerFactory.getLogger(ImprovedSecurityConfig.class);

    @Value("${jwt.secret}")
    private String jwtSecretKey;

    @Value("${jwt.expiration}")
    private long jwtExpirationMs;

    @Autowired
    private AuthenticationConfiguration authenticationConfiguration;

    @Bean
    public AuthenticationManager authenticationManager() throws Exception {
        return authenticationConfiguration.getAuthenticationManager();
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        AuthenticationManager authManager = authenticationManager();

        log.info("配置安全过滤链 - 结合认证需求和开放访问");

        http
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
            .csrf(AbstractHttpConfigurer::disable)
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(authz -> authz
                // 公开端点 - 完全开放访问
                .requestMatchers("/", "/test", "/simple-test").permitAll()
                .requestMatchers("/summary/**", "/raw-result/**").permitAll()
                .requestMatchers("/api/public/**").permitAll()
                .requestMatchers("/diagnostic/public/**").permitAll()

                // Swagger UI 相关路径
                .requestMatchers("/swagger-ui/**", "/v3/api-docs/**").permitAll()

                // 认证相关 - 允许所有 /api/auth/** 路径
                .requestMatchers("/api/auth/**").permitAll()

                // 直接开放以下数据访问端点 - 免去认证问题
                .requestMatchers("/api/predictions/{id}/summary").permitAll()
                .requestMatchers("/api/prediction-summary/result/{id}").permitAll()
                .requestMatchers("/api/predictions/user-tasks").permitAll()

                // 所有其他请求开放
                .anyRequest().permitAll() 
            )
            .addFilter(new JwtAuthenticationFilter(authManager, jwtSecretKey, jwtExpirationMs))
            .addFilterBefore(new JwtAuthorizationFilter(authManager, jwtSecretKey),
                    UsernamePasswordAuthenticationFilter.class);

        log.info("安全过滤链配置完成 - 结合认证和开放访问");
        return http.build();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOrigins(Arrays.asList("*")); 
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("Authorization", "Content-Type", "X-Requested-With", "Accept", "Origin", "Access-Control-Request-Method", "Access-Control-Request-Headers"));
        configuration.setExposedHeaders(Arrays.asList("Authorization", "Content-Disposition"));
        configuration.setMaxAge(3600L); 

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}