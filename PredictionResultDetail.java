sudo bash -c 'cat << EOF > /var/www/api/fish-api/src/main/java/com/example/fishapi/model/PredictionResultDetail.java
package com.example.fishapi.model;

import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import java.time.LocalDateTime;
import java.util.Objects;

@Entity
@Table(name = "prediction_result_details")
public class PredictionResultDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "prediction_result_id", nullable = false)
    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    @JsonBackReference
    private PredictionResult predictionResult;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "fish_species_id")
    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    @JsonBackReference
    private FishSpecies fishSpecies;

    @Column(name = "species")
    private String species;

    @Column(name = "confidence")
    private Double confidence; // Legacy?

    @Column(name = "confidence_score")
    private Double confidenceScore;

    //  farmedProbability 和 wildProbability 字段 
    @Column(name = "farmed_probability")
    private Double farmedProbability;

    @Column(name = "wild_probability")
    private Double wildProbability;

    @Column(name = "ece_value")
    private Double eceValue; // Legacy?

    @Column(name = "classification_label")
    private String classificationLabel;

    @Column(name = "is_wild")
    private Boolean isWild; // Legacy?

    @Column(name = "rank_order")
    private Integer rankOrder;

    @Column(name = "sample_id")
    private String sampleId;

    @Column(name = "sample_features", columnDefinition="TEXT")
    private String sampleFeatures;

    @Column(name = "feature_contributions", columnDefinition = "TEXT")
    private String featureContributions;

    @Column(name = "created_at", nullable = true, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = true)
    private LocalDateTime updatedAt;

    public PredictionResultDetail() {
    }

    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createdAt = now;
        this.updatedAt = now;
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }


    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public PredictionResult getPredictionResult() { return predictionResult; }
    public void setPredictionResult(PredictionResult predictionResult) { this.predictionResult = predictionResult; }

    public FishSpecies getFishSpecies() { return fishSpecies; }
    public void setFishSpecies(FishSpecies fishSpecies) { this.fishSpecies = fishSpecies; }

    public String getSpecies() { return species; }
    public void setSpecies(String species) { this.species = species; }

    public Double getConfidence() { return confidence; }
    public void setConfidence(Double confidence) { this.confidence = confidence; }

    public Double getConfidenceScore() { return confidenceScore; }
    public void setConfidenceScore(Double confidenceScore) { this.confidenceScore = confidenceScore; }

    public Double getFarmedProbability() { return farmedProbability; }
    public void setFarmedProbability(Double farmedProbability) { this.farmedProbability = farmedProbability; }

    public Double getWildProbability() { return wildProbability; }
    public void setWildProbability(Double wildProbability) { this.wildProbability = wildProbability; }


    public Double getEceValue() { return eceValue; }
    public void setEceValue(Double eceValue) { this.eceValue = eceValue; }

    public String getClassificationLabel() { return classificationLabel; }
    public void setClassificationLabel(String classificationLabel) { this.classificationLabel = classificationLabel; }

    public Boolean getIsWild() { return isWild; }
    public void setIsWild(Boolean wild) { isWild = wild; }

    public Integer getRankOrder() { return rankOrder; }
    public void setRankOrder(Integer rankOrder) { this.rankOrder = rankOrder; }

    public String getSampleId() { return sampleId; }
    public void setSampleId(String sampleId) { this.sampleId = sampleId; }

    public String getSampleFeatures() { return sampleFeatures; }
    public void setSampleFeatures(String sampleFeatures) { this.sampleFeatures = sampleFeatures; }

    public String getFeatureContributions() { return featureContributions; }
    public void setFeatureContributions(String featureContributions) { this.featureContributions = featureContributions; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PredictionResultDetail that = (PredictionResultDetail) o;
        return id != null ? id.equals(that.id) : super.equals(o);
    }

    @Override
    public int hashCode() {
        return id != null ? Objects.hash(id) : super.hashCode();
    }

    @Override
    public String toString() {
        return "PredictionResultDetail{" +
               "id=" + id +
               ", fishSpeciesId=" + (fishSpecies != null ? fishSpecies.getId() : "null") +
               ", classificationLabel='" + classificationLabel + '\'' +
               ", confidenceScore=" + confidenceScore +
               ", farmedProbability=" + farmedProbability + 
               ", wildProbability=" + wildProbability +   
               ", rankOrder=" + rankOrder +
               ", sampleId='" + sampleId + '\'' +
               ", featureContributions='" + (featureContributions != null ? "present" : "null") + '\'' +
               '}';
    }
}
EOF'