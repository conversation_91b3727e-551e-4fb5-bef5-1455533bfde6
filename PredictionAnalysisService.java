package com.example.fishapi.service;

import com.example.fishapi.dto.PredictionSummaryDTO;
import com.example.fishapi.model.PredictionResult;
import com.example.fishapi.model.PredictionResultDetail;
import com.example.fishapi.model.PredictionSummary;
import com.example.fishapi.model.PredictionTask;
import com.example.fishapi.model.PredictionTask.TaskStatus;
import com.example.fishapi.repository.PredictionResultRepository;
import com.example.fishapi.repository.PredictionResultDetailRepository;
import com.example.fishapi.repository.PredictionSummaryRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors; // Added for Collectors

@Service
@Slf4j
public class PredictionAnalysisService {

    private final PredictionResultRepository resultRepository;
    private final PredictionResultDetailRepository detailRepository;
    private final PredictionSummaryRepository summaryRepository;
    private final PredictionTaskService predictionTaskService;

    @Autowired
    public PredictionAnalysisService(
            PredictionResultRepository resultRepository,
            PredictionResultDetailRepository detailRepository,
            PredictionSummaryRepository summaryRepository,
            @Lazy PredictionTaskService predictionTaskService) {
        this.resultRepository = resultRepository;
        this.detailRepository = detailRepository;
        this.summaryRepository = summaryRepository;
        this.predictionTaskService = predictionTaskService;
    }

    @Transactional
    public PredictionSummary generateSummaryForTask(Long taskId) {
        log.info("开始为任务 {} 生成摘要", taskId);

        Optional<PredictionResult> resultOpt = resultRepository.findByPredictionTask_Id(taskId);
        if (resultOpt.isEmpty()) {
            log.warn("未找到任务ID为 {} 的预测结果，无法生成摘要", taskId);
            return null;
        }
        PredictionResult result = resultOpt.get();

        List<PredictionResultDetail> details = detailRepository.findByPredictionResult(result);
        if (details.isEmpty()) {
            log.warn("未找到预测结果ID为 {} (任务 {}) 的预测详情，无法生成完整摘要", result.getId(), taskId);
            PredictionSummary summary = summaryRepository.findByPredictionTask_Id(taskId)
                                          .orElse(new PredictionSummary());
             summary.setPredictionTask(result.getPredictionTask());
             summary.setPredictionResult(result);
             summary.setTopClassificationLabel("无详细结果");
             summary.setTotalSpeciesCount(0);
             summary.setWildCount(0);
             summary.setFarmedCount(0);
             summary.setWildPercentage(BigDecimal.ZERO);
             summary.setFarmedPercentage(BigDecimal.ZERO);
             PredictionSummary savedSummary = summaryRepository.save(summary);
             return savedSummary;
        }

        int totalResults = details.size();
        int wildCount = 0;
        int farmedCount = 0;
        BigDecimal totalConfidence = BigDecimal.ZERO;
        PredictionResultDetail topDetail = null;

        for (PredictionResultDetail detail : details) {
            String label = detail.getClassificationLabel();
            Double confidenceScore = detail.getConfidenceScore();

            if (StringUtils.hasText(label)) {
                if (label.startsWith("野生")) {
                    wildCount++;
                } else if (label.startsWith("养殖")) {
                    farmedCount++;
                }
            }

            if (confidenceScore != null) {
                totalConfidence = totalConfidence.add(BigDecimal.valueOf(confidenceScore));
            }

            // Find top detail (prioritize rank 1, fallback to highest confidence)
            if (detail.getRankOrder() != null && detail.getRankOrder() == 1) {
                 topDetail = detail; // Found rank 1, use it
            } else if (topDetail == null || (topDetail.getRankOrder() != null && topDetail.getRankOrder() != 1)) {
                 // If current topDetail is null or not rank 1, compare confidence
                 if (confidenceScore != null && (topDetail == null || topDetail.getConfidenceScore() == null || confidenceScore > topDetail.getConfidenceScore())) {
                     topDetail = detail;
                 }
            }
             // If rank 1 was already found, don't overwrite with non-rank-1 even if confidence is higher
        }
         // If after loop topDetail is still null (e.g., all details have null confidence and rank), pick first one
        if(topDetail == null && !details.isEmpty()){
             topDetail = details.get(0);
        }


        BigDecimal wildPercentage = totalResults > 0 ?
                BigDecimal.valueOf(wildCount * 100.0 / totalResults).setScale(2, RoundingMode.HALF_UP) :
                BigDecimal.ZERO;

        BigDecimal farmedPercentage = totalResults > 0 ?
                BigDecimal.valueOf(farmedCount * 100.0 / totalResults).setScale(2, RoundingMode.HALF_UP) :
                BigDecimal.ZERO;

        String dominantLabel = "未知";
        if (wildCount > farmedCount) {
            dominantLabel = "野生为主";
        } else if (farmedCount > wildCount) {
            dominantLabel = "养殖为主";
        } else if (wildCount == farmedCount && wildCount > 0) {
            dominantLabel = "混合";
        } else if (!details.isEmpty() && StringUtils.hasText(details.get(0).getClassificationLabel())){
             // Use first label if counts are zero but details exist
            dominantLabel = details.get(0).getClassificationLabel();
        }


        BigDecimal avgConfidence = totalResults > 0 ?
                totalConfidence.divide(BigDecimal.valueOf(totalResults), 9, RoundingMode.HALF_UP) :
                BigDecimal.ZERO;

        PredictionSummary summary = summaryRepository.findByPredictionTask_Id(taskId)
                                          .orElse(new PredictionSummary());

        summary.setPredictionTask(result.getPredictionTask());
        summary.setPredictionResult(result);

        summary.setTopClassificationLabel(topDetail != null ? topDetail.getClassificationLabel() : dominantLabel);
        summary.setTopClassificationConfidence(topDetail != null && topDetail.getConfidenceScore() != null ? BigDecimal.valueOf(topDetail.getConfidenceScore()).setScale(9, RoundingMode.HALF_UP) : avgConfidence);

        // *** 修正1: Cast long count to int ***
        summary.setTotalSpeciesCount((int) details.stream()
                                              .map(PredictionResultDetail::getFishSpecies)
                                              .filter(fs -> fs != null)
                                              .distinct()
                                              .count()); // Cast to int
        summary.setWildCount(wildCount);
        summary.setFarmedCount(farmedCount);
        summary.setWildPercentage(wildPercentage);
        summary.setFarmedPercentage(farmedPercentage);
        summary.setAnalysisTimeSeconds(result.getAnalysisTimeSeconds());

        PredictionSummary savedSummary = summaryRepository.save(summary);
        log.info("成功保存任务 {} 的摘要，ID: {}, 野生={}, 养殖={}, 总数={}, TopLabel: {}",
                taskId, savedSummary.getId(), wildCount, farmedCount, summary.getTotalSpeciesCount(), summary.getTopClassificationLabel());

        try {
            PredictionTask task = result.getPredictionTask();
            if (task != null && task.getStatus() != TaskStatus.COMPLETED && task.getStatus() != TaskStatus.FAILED) {
                log.info("摘要生成成功，更新任务 {} 状态为 COMPLETED", taskId);
                // *** 修正2: Call correct method signature ***
                predictionTaskService.updatePredictionTaskStatus(taskId, TaskStatus.COMPLETED);
                 log.info("任务 {} 状态更新请求已发送。", taskId); // Log separately
            } else {
                 log.info("任务 {} 状态已经是 {}，无需更新。", taskId, task != null ? task.getStatus() : "N/A");
            }
        } catch (Exception e) {
            log.error("更新任务 {} 状态时出错: {}", taskId, e.getMessage(), e);
        }

        return savedSummary;
    }

    public PredictionSummaryDTO getSummaryDTOForTask(Long taskId) {
        log.info("获取任务 {} 的摘要DTO", taskId);

        PredictionSummary summary = summaryRepository.findByPredictionTask_Id(taskId)
                .orElseGet(() -> {
                    log.info("任务 {} 摘要不存在，尝试生成...", taskId);
                    return generateSummaryForTask(taskId);
                 });

        if (summary == null) {
             log.error("无法为任务 {} 找到或生成摘要", taskId);
             return null;
        }

        PredictionSummaryDTO dto = new PredictionSummaryDTO();
        dto.setId(summary.getId());
        dto.setTaskId(taskId);
        if (summary.getPredictionResult() != null) { // Check if result link exists
             dto.setPredictionResultId(summary.getPredictionResult().getId());
        } else {
             log.warn("任务 {} 的摘要存在但没有关联的预测结果", taskId);
             dto.setPredictionResultId(null);
        }
        dto.setTopClassificationLabel(summary.getTopClassificationLabel());
        dto.setTopClassificationConfidence(summary.getTopClassificationConfidence());
        dto.setTotalSpeciesCount(summary.getTotalSpeciesCount());
        dto.setWildCount(summary.getWildCount());
        dto.setFarmedCount(summary.getFarmedCount());
        dto.setWildPercentage(summary.getWildPercentage());
        dto.setFarmedPercentage(summary.getFarmedPercentage());
        dto.setAnalysisTimeSeconds(summary.getAnalysisTimeSeconds());
        dto.setCreatedAt(summary.getCreatedAt());
        dto.setUpdatedAt(summary.getUpdatedAt());

        List<PredictionSummaryDTO.DetailedResult> detailedResultsDto = new ArrayList<>();
        if (summary.getPredictionResult() != null) { // Only fetch details if result exists
            List<PredictionResultDetail> details = detailRepository.findByPredictionResult(summary.getPredictionResult());
            for (PredictionResultDetail detail : details) {
                 if (detail == null) continue;

                PredictionSummaryDTO.DetailedResult resultDtoItem = new PredictionSummaryDTO.DetailedResult();
                resultDtoItem.setSpecies(detail.getSpecies() != null ? detail.getSpecies() : (detail.getFishSpecies() != null ? detail.getFishSpecies().getSpeciesName() : "未知"));
                resultDtoItem.setClassification(detail.getClassificationLabel());
                resultDtoItem.setConfidence(detail.getConfidenceScore() != null ? BigDecimal.valueOf(detail.getConfidenceScore()) : null);

                BigDecimal relevantProbability = null;
                if (detail.getClassificationLabel() != null) {
                    if (detail.getClassificationLabel().startsWith("养殖") && detail.getFarmedProbability() != null) {
                        relevantProbability = BigDecimal.valueOf(detail.getFarmedProbability());
                    } else if (detail.getClassificationLabel().startsWith("野生") && detail.getWildProbability() != null) {
                        relevantProbability = BigDecimal.valueOf(detail.getWildProbability());
                    }
                }
                 if (relevantProbability == null) {
                     log.trace("No specific probability found for label '{}', setting probability in DTO to null.", detail.getClassificationLabel());
                 }
                resultDtoItem.setProbability(relevantProbability); // Expects setProbability(BigDecimal)
                detailedResultsDto.add(resultDtoItem);
            }
        } else {
            log.warn("无法为任务 {} 的摘要DTO填充详细结果，因为缺少关联的预测结果", taskId);
        }


        dto.setDetailedResults(detailedResultsDto);
        log.info("成功生成任务 {} 的摘要DTO，包含 {} 个详细结果", taskId, detailedResultsDto.size());

        return dto;
    }

    /**
     * @deprecated Use {@link #getSummaryDTOForTask(Long)} instead.
     */
    @Deprecated
    public PredictionSummaryDTO getSummaryDTO(Long resultId) {
        log.warn("getSummaryDTO(resultId={}) is deprecated. Use getSummaryDTOForTask(taskId) instead.", resultId);
        Optional<PredictionResult> resultOpt = resultRepository.findById(resultId);
        if (resultOpt.isEmpty()) {
            log.warn("未找到ID为 {} 的预测结果 (Deprecated method)", resultId);
            return null;
        }
        PredictionTask task = resultOpt.get().getPredictionTask();
        if (task == null) {
             log.warn("预测结果 ID {} 没有关联的任务 (Deprecated method)", resultId);
             return null;
        }
        return getSummaryDTOForTask(task.getId());
    }

    /**
     * @deprecated Use {@link #generateSummaryForTask(Long)} instead.
     */
    @Deprecated
    @Transactional
    public PredictionSummary generateSummary(Long resultId) {
        log.warn("generateSummary(resultId={}) is deprecated. Use generateSummaryForTask(taskId) instead.", resultId);
        Optional<PredictionResult> resultOpt = resultRepository.findById(resultId);
        if (resultOpt.isEmpty()) {
            log.warn("未找到ID为 {} 的预测结果 (Deprecated method)", resultId);
            return null;
        }
         PredictionTask task = resultOpt.get().getPredictionTask();
        if (task == null) {
             log.warn("预测结果 ID {} 没有关联的任务 (Deprecated method)", resultId);
             return null;
        }
        return generateSummaryForTask(task.getId());
    }
}