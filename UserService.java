cat > /var/www/api/fish-api/src/main/java/com/example/fishapi/service/UserService.java << EOF
package com.example.fishapi.service;

import com.example.fishapi.dto.ForgotPasswordRequest;
import com.example.fishapi.dto.ResetPasswordRequest;
import com.example.fishapi.dto.SendCodeRequest;
import org.springframework.security.core.Authentication; // *** 添加导入 ***

public interface UserService {

    /**
     * 发起密码重置流程 (给已存在的邮箱)
     * @param request 包含用户邮箱
     */
    void initiatePasswordReset(ForgotPasswordRequest request);

    /**
     * 使用验证码重置密码
     * @param request 包含邮箱、验证码和新密码
     */
    void resetPassword(ResetPasswordRequest request);

    /**
     * 发送注册验证码 (给未注册的邮箱)
     * @param request 包含用户邮箱
     */
    void sendRegistrationVerificationCode(SendCodeRequest request);

    /**
     * 验证注册码是否有效 (用于注册流程)
     * @param email 邮箱
     * @param code 验证码
     * @return boolean 是否有效
     */
    boolean verifyRegistrationCode(String email, String code);

    /**
     * 删除指定用户账户
     * @param userIdToDelete 要删除的用户ID
     * @param principal 执行操作的用户认证信息 (用于权限检查)
     * @throws SecurityException 如果无权删除
     * @throws com.example.fishapi.exception.ResourceNotFoundException 如果用户未找到 (确保异常类型匹配)
     */
    void deleteUser(Long userIdToDelete, Authentication principal); // *** 新增方法声明 ***

} // 接口结束
EOF