sudo cat << 'EOF' > /var/www/api/fish-api/src/main/java/com/example/fishapi/service/impl/PredictionTaskServiceImpl.java
package com.example.fishapi.service.impl;

import com.example.fishapi.client.FlaskApiClient;
import com.example.fishapi.dto.PredictionTaskDto;
import com.example.fishapi.exception.ResourceNotFoundException;
import com.example.fishapi.model.PredictionTask;
import com.example.fishapi.model.User;
import com.example.fishapi.model.PredictionTask.TaskStatus;
import com.example.fishapi.repository.PredictionTaskRepository;
import com.example.fishapi.repository.UserRepository;
import com.example.fishapi.service.FileStorageService;
import com.example.fishapi.service.PredictionResultService;
import com.example.fishapi.service.PredictionTaskService;
import jakarta.persistence.EntityNotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.context.ApplicationEventPublisher;

import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PredictionTaskServiceImpl implements PredictionTaskService {

    private final PredictionTaskRepository predictionTaskRepository;
    private final UserRepository userRepository;
    private final FileStorageService fileStorageService;
    private final FlaskApiClient flaskApiClient;
    private final PredictionResultService predictionResultService;
    private final ApplicationEventPublisher eventPublisher;

    @Autowired
    public PredictionTaskServiceImpl(PredictionTaskRepository predictionTaskRepository,
                                     UserRepository userRepository,
                                     FileStorageService fileStorageService,
                                     FlaskApiClient flaskApiClient,
                                     @Lazy PredictionResultService predictionResultService,
                                     ApplicationEventPublisher eventPublisher
                                     ) {
        this.predictionTaskRepository = predictionTaskRepository;
        this.userRepository = userRepository;
        this.fileStorageService = fileStorageService;
        this.flaskApiClient = flaskApiClient;
        this.predictionResultService = predictionResultService;
        this.eventPublisher = eventPublisher;
    }

    @Override
    public Page<PredictionTaskDto> getUserPredictionTasks(String username, Pageable pageable) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("未找到用户: " + username));
        Page<PredictionTask> tasks = predictionTaskRepository.findByUser(user, pageable);
        return tasks.map(this::convertToDto);
    }

    @Override
    public PredictionTaskDto getPredictionTaskById(Long id, String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("未找到用户: " + username));
        PredictionTask task = predictionTaskRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("未找到预测任务: " + id));
        if (!task.getUser().getId().equals(user.getId())) {
            log.warn("用户 {} 尝试访问不属于自己的任务 {}", username, id);
            throw new SecurityException("无权访问此预测任务");
        }
        return convertToDto(task);
    }

    @Override
    @Transactional
    public PredictionTaskDto createPredictionTask(MultipartFile file, String fishType, String taskName, String username) {
        log.info("用户 {} 正在创建预测任务 {}, 类型: {}, 文件: {}", username, taskName, fishType, file.getOriginalFilename());

        if (file == null || file.isEmpty()) {
             log.error("用户 {} 尝试创建任务 {} 时上传了空文件或文件对象为null", username, taskName);
             throw new IllegalArgumentException("上传的文件不能为空");
        }

        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("未找到用户: " + username));

        PredictionTask task = new PredictionTask();
        task.setUser(user);
        task.setTaskName(taskName);
        task.setFishType(fishType);
        task.setFileName(file.getOriginalFilename());
        task.setFileType(file.getContentType());
        task.setStatus(TaskStatus.PENDING);

        PredictionTask savedTask = predictionTaskRepository.save(task);
        log.info("预测任务已创建并保存, ID: {}", savedTask.getId());

        String storedFilePath = null;
        try {
             String storedFileName = String.format("user_%d_task_%d_%s_%s",
                                                   user.getId(),
                                                   savedTask.getId(),
                                                   fishType,
                                                   file.getOriginalFilename().replaceAll("[^a-zA-Z0-9._-]", "_"));
             Path filePath = fileStorageService.storeFile(file, storedFileName);
             storedFilePath = filePath.toString();
             log.info("文件已保存到: {}", storedFilePath);

             updateTaskStatusAndStoredFileName(savedTask.getId(), TaskStatus.PROCESSING, storedFileName);

             triggerPrediction(savedTask.getId(), storedFilePath, fishType, username);

             log.info("任务 {} 的预测流程已触发。", savedTask.getId());

        } catch (Exception e) {
             log.error("创建任务 {} 时处理文件或触发预测失败: {}", savedTask.getId(), e.getMessage(), e);
             updatePredictionTaskStatus(savedTask.getId(), TaskStatus.FAILED);
             throw new RuntimeException("创建预测任务失败：无法处理上传的文件或触发预测。", e);
        }

        PredictionTask updatedTask = predictionTaskRepository.findById(savedTask.getId()).orElse(savedTask);
        return convertToDto(updatedTask);
    }

    @Override
    @Transactional
    public void updatePredictionTaskStatus(Long taskId, TaskStatus status) {
        log.info("正在更新任务 {} 的状态为: {}", taskId, status);
        try {
            PredictionTask task = predictionTaskRepository.findById(taskId)
                    .orElseThrow(() -> new EntityNotFoundException("未找到预测任务: " + taskId));
            log.info("找到任务 {}, 当前状态: {}, 将更新为: {}", taskId, task.getStatus(), status);
            task.setStatus(status);
             if (status == TaskStatus.COMPLETED) {
                 task.setCompletedAt(LocalDateTime.now());
                 log.info("任务 {} 状态为 COMPLETED，设置完成时间", taskId);
             }
            PredictionTask savedTask = predictionTaskRepository.save(task);
            log.info("任务 {} 状态已成功更新为: {}, 数据库记录ID: {}", taskId, savedTask.getStatus(), savedTask.getId());
        } catch (Exception e) {
            log.error("更新任务 {} 状态为 {} 时出错: {}", taskId, status, e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public void updatePredictionTaskStatus(Long taskId, String statusString) {
        try {
            log.info("尝试将状态字符串 {} 转换为枚举并应用于任务 {}", statusString, taskId);
            TaskStatus status = TaskStatus.valueOf(statusString.toUpperCase());
            updatePredictionTaskStatus(taskId, status);
        } catch (IllegalArgumentException e) {
            log.error("尝试将无效的状态字符串 {} 应用于任务 {}: {}", statusString, taskId, e.getMessage(), e);
        } catch (Exception e) {
            log.error("更新任务 {} 状态时发生意外错误 (来自字符串): {}", taskId, e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public void updateTaskStatus(Long taskId, String statusString, String username) {
        log.info("更新任务状态 (带用户名验证): ID={}, 状态={}, 用户={}", taskId, statusString, username);
         getPredictionTaskById(taskId, username);
         updatePredictionTaskStatus(taskId, statusString);
    }

    @Transactional
    public void updateTaskStatusAndStoredFileName(Long taskId, TaskStatus status, String storedFileName) {
         PredictionTask task = predictionTaskRepository.findById(taskId)
                .orElseThrow(() -> new EntityNotFoundException("未找到预测任务: " + taskId));
         task.setStatus(status);
         task.setStoredFileName(storedFileName);
         if (status == TaskStatus.COMPLETED) {
             task.setCompletedAt(LocalDateTime.now());
             log.info("任务 {} 状态为 COMPLETED (更新文件名时)，设置完成时间", taskId);
         }
         predictionTaskRepository.save(task);
         log.info("任务 {} 状态更新为: {}, 存储文件名更新为: {}", taskId, status, storedFileName);
    }

    @Override
    @Transactional
    public void updateTaskStatusAndFilePath(Long taskId, String statusString, String filePath) {
         try {
            TaskStatus status = TaskStatus.valueOf(statusString.toUpperCase());
            updateTaskStatusAndStoredFileName(taskId, status, filePath);
        } catch (IllegalArgumentException e) {
            log.error("尝试将无效的状态字符串 {} 应用于任务 {} (更新文件路径时)", statusString, taskId, e);
        } catch (Exception e) {
             log.error("更新任务 {} 状态和文件路径时发生意外错误: {}", taskId, e.getMessage(), e);
        }
    }

    @Async
    @Override
    public void triggerPrediction(Long taskId, String filePath, String fishType, String username) {
        log.info("异步触发任务 {} 的预测 (用户: {}), 文件: {}, 类型: {}", taskId, username, filePath, fishType);
        String rawResponse = null;
        try {
            log.info("任务 {}：准备调用 Flask API 进行预测 (发送文件路径)", taskId);
            rawResponse = flaskApiClient.predict(filePath, fishType);
            if (rawResponse == null) {
                log.error("任务 {}：Flask API 调用返回了 null 响应！", taskId);
                throw new RuntimeException("Flask API prediction returned null response for task " + taskId);
            }
            log.info("任务 {}：Flask API 调用完成。响应体长度: {}", taskId, rawResponse.length());

            log.info("任务 {}：准备调用 createOrUpdatePredictionResult 保存结果", taskId);
            predictionResultService.createOrUpdatePredictionResult(taskId, rawResponse, username);
            log.info("任务 {}：预测结果处理流程看似完成 (结果服务已调用)", taskId);

        } catch (EntityNotFoundException enfe) {
             log.error("任务 {} (用户: {}): 预测触发失败，未找到任务实体: {}", taskId, username, enfe.getMessage());
        } catch (Exception e) {
            log.error("任务 {} (用户: {}) 异步调用 Flask API 或处理结果失败: {}", taskId, username, e.getMessage(), e);
            try {
                updatePredictionTaskStatus(taskId, TaskStatus.FAILED);
                log.info("任务 {} 状态已尝试更新为 FAILED", taskId);
                if (rawResponse == null) {
                     log.warn("任务 {}：由于原始响应为 null，尝试保存错误信息作为预测结果", taskId);
                     String errorResponse = String.format("{\"error\":\"Prediction failed in triggerPrediction: %s\"}", e.getMessage().replace("\"", "\\\""));
                     try {
                         predictionResultService.createOrUpdatePredictionResult(taskId, errorResponse, username);
                         log.info("任务 {}：已尝试保存错误响应", taskId);
                     } catch (Exception saveEx) {
                         log.error("任务 {}：保存错误响应时再次发生错误: {}", taskId, saveEx.getMessage(), saveEx);
                     }
                } else {
                     log.warn("任务 {}：预测过程中发生错误，但 Flask API 已返回响应，结果服务将处理或已处理。", taskId);
                }
            } catch (Exception innerEx) {
                log.error("任务 {} (用户: {}) 在处理异步预测失败时再次发生错误: {}", taskId, username, innerEx.getMessage(), innerEx);
            }
        }
    }

    private PredictionTaskDto convertToDto(PredictionTask task) {
        PredictionTaskDto dto = new PredictionTaskDto();
        dto.setId(task.getId());
        dto.setTaskName(task.getTaskName());
        dto.setFishType(task.getFishType());
        dto.setStatus(task.getStatus());
        dto.setStoredFileName(task.getStoredFileName());
        dto.setCreatedAt(task.getCreatedAt());
        dto.setUpdatedAt(task.getUpdatedAt());
        if (task.getUser() != null) {
            dto.setUserId(task.getUser().getId());
        }
        return dto;
    }


    @Override
    @Transactional
    public void deletePredictionTask(Long taskId, String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("未找到用户: " + username));

        PredictionTask task = predictionTaskRepository.findById(taskId)
                .orElseThrow(() -> new ResourceNotFoundException("未找到预测任务: " + taskId));

        if (!task.getUser().getId().equals(user.getId())) {
            log.warn("用户 {} 尝试删除不属于自己的任务 {}", username, taskId);
            throw new SecurityException("无权删除此预测任务");
        }

        predictionTaskRepository.delete(task);
        log.info("用户 {} 成功删除任务 {}", username, taskId);
    }

    @Override
    @Transactional
    public int deletePredictionTasks(List<Long> taskIds, String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("未找到用户: " + username));

        List<PredictionTask> tasksToDelete = predictionTaskRepository.findAllById(taskIds);
        int count = 0;
        List<PredictionTask> ownedTasksToDelete = new ArrayList<>();

        for (PredictionTask task : tasksToDelete) {
            if (task.getUser().getId().equals(user.getId())) {
                 ownedTasksToDelete.add(task);
                 count++;
            } else {
                 log.warn("用户 {} 尝试删除不属于自己的任务 {}", username, task.getId());
            }
        }

        if (!ownedTasksToDelete.isEmpty()) {

            predictionTaskRepository.deleteAll(ownedTasksToDelete);
            log.info("用户 {} 成功删除了 {} 个任务", username, count);
        } else {
             log.info("用户 {} 没有有效的任务需要删除（在提供的 ID 列表中）", username);
        }

        return count;
    }

    @Override
    @Transactional
    public int deleteAllUserPredictionTasks(String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("未找到用户: " + username));

        Page<PredictionTask> tasksPage = predictionTaskRepository.findByUser(user, Pageable.unpaged());
        List<PredictionTask> tasksToDelete = tasksPage.getContent();

        int count = tasksToDelete.size();

        if (!tasksToDelete.isEmpty()) {
            log.info("用户 {} 准备删除 {} 个任务...", username, count);

            predictionTaskRepository.deleteAll(tasksToDelete);
            log.info("用户 {} 成功清空了所有任务，共 {} 个", username, count);
        } else {
            log.info("用户 {} 没有需要删除的任务", username);
        }

        return count;
    }


    // 实现新增的接口方法
    @Override
    @Transactional(readOnly = true)
    public List<PredictionTaskDto> getTasksByUserId(Long userId) {
        log.debug("Fetching all prediction tasks for user ID: {}", userId);

        // 1. 调用 Repository 方法获取实体列表
        List<PredictionTask> tasks = predictionTaskRepository.findByUserIdOrderByCreatedAtDesc(userId);

        // 2. 将实体列表转换为 DTO 列表
        List<PredictionTaskDto> taskDtos = tasks.stream()
                                                .map(this::convertToDto)
                                                .collect(Collectors.toList());

        log.info("Found {} tasks for user ID: {}", taskDtos.size(), userId);
        return taskDtos;
    }

}
