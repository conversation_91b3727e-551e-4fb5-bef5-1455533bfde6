cat > /var/www/api/fish-api/src/main/java/com/example/fishapi/controller/AuthController.java << EOF
package com.example.fishapi.service;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

@Service
public class VaptchaService {

    private static final Logger log = LoggerFactory.getLogger(VaptchaService.class);

    @Value("${vaptcha.vid}")
    private String vaptchaVid;

    @Value("${vaptcha.secretkey}")
    private String vaptchaSecretKey;

    @Value("${vaptcha.api.url}")
    private String vaptchaApiUrl; // 使用配置文件中的固定 URL

    private final RestTemplate restTemplate;

    public VaptchaService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    /**
     * 调用 VAPTCHA API 进行二次验证
     * @param token 前端获取的 VAPTCHA token
     * @param clientIp 请求用户的 IP 地址
     * @return true 如果验证成功，false 如果失败或出错
     */
    public boolean verify(String token, String clientIp) {
        log.debug("Verifying VAPTCHA token: {} for IP: {}", token, clientIp);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        VaptchaVerifyRequest requestBody = new VaptchaVerifyRequest(
                vaptchaVid,
                vaptchaSecretKey,
                0, // Scene 0
                token,
                clientIp
        );

        HttpEntity<VaptchaVerifyRequest> request = new HttpEntity<>(requestBody, headers);

        try {
            ResponseEntity<VaptchaResponse> response = restTemplate.postForEntity(
                    vaptchaApiUrl, request, VaptchaResponse.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                VaptchaResponse vaptchaResponse = response.getBody();
                log.info("VAPTCHA API response: success={}, score={}, msg={}",
                         vaptchaResponse.getSuccess(), vaptchaResponse.getScore(), vaptchaResponse.getMsg());

                boolean success = vaptchaResponse.getSuccess() == 1;
                if (!success) {
                    log.warn("VAPTCHA verification failed for token: {}. Reason: {}", token, vaptchaResponse.getMsg());
                }
                return success;
            } else {
                log.error("VAPTCHA API request failed with status: {} and body: {}", response.getStatusCode(), response.getBody());
                return false;
            }
        } catch (RestClientException e) {
            log.error("Error calling VAPTCHA API at {}: {}", vaptchaApiUrl, e.getMessage(), e);
            return false;
        } catch (Exception e) {
             log.error("Unexpected error during VAPTCHA verification for token {}: {}", token, e.getMessage(), e);
             return false;
        }
    }

    private static class VaptchaVerifyRequest {
        private String id;
        private String secretkey;
        private int scene;
        private String token;
        private String ip;

        public VaptchaVerifyRequest(String id, String secretkey, int scene, String token, String ip) {
            this.id = id;
            this.secretkey = secretkey;
            this.scene = scene;
            this.token = token;
            this.ip = ip;
        }

        public String getId() { return id; }
        public String getSecretkey() { return secretkey; }
        public int getScene() { return scene; }
        public String getToken() { return token; }
        public String getIp() { return ip; }
    }


    private static class VaptchaResponse {
        @JsonProperty("success")
        private int success;
        @JsonProperty("score")
        private int score;
        @JsonProperty("msg")
        private String msg;

        public int getSuccess() { return success; }
        public void setSuccess(int success) { this.success = success; }
        public int getScore() { return score; }
        public void setScore(int score) { this.score = score; }
        public String getMsg() { return msg; }
        public void setMsg(String msg) { this.msg = msg; }
    }
}
EOF