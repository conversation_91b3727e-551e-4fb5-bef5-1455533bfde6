/var/www/api/fish-api/src/main/java/com/example/fishapi/exception/InvalidTokenException.java
package com.example.fishapi.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.BAD_REQUEST) // 返回400错误
public class InvalidTokenException extends RuntimeException {
    public InvalidTokenException(String message) {
        super(message);
    }
}