# sudo bash -c 'cat << EOF > /var/www/api/fish-api/src/main/java/com/example/fishapi/dto/PredictionResultDetailDto.java

package com.example.fishapi.dto;

import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.Objects;

@NoArgsConstructor
@AllArgsConstructor
public class PredictionResultDetailDto {
    private Long id;
    private Long fishSpeciesId;
    private String fishSpeciesName;
    private Double confidenceScore;
    private Integer rankOrder;
    private String sampleId;
    private String classificationLabel;
    private String sampleFeatures;
    private String featureContributions;

    private Double farmedProbability;
    private Double wildProbability;

    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getFishSpeciesId() { return fishSpeciesId; }
    public void setFishSpeciesId(Long fishSpeciesId) { this.fishSpeciesId = fishSpeciesId; }

    public String getFishSpeciesName() { return fishSpeciesName; }
    public void setFishSpeciesName(String fishSpeciesName) { this.fishSpeciesName = fishSpeciesName; }

    public Double getConfidenceScore() { return confidenceScore; }
    public void setConfidenceScore(Double confidenceScore) { this.confidenceScore = confidenceScore; }

    public Integer getRankOrder() { return rankOrder; }
    public void setRankOrder(Integer rankOrder) { this.rankOrder = rankOrder; }

    public String getSampleId() { return sampleId; }
    public void setSampleId(String sampleId) { this.sampleId = sampleId; }

    public String getClassificationLabel() { return classificationLabel; }
    public void setClassificationLabel(String classificationLabel) { this.classificationLabel = classificationLabel; }

    public String getSampleFeatures() { return sampleFeatures; }
    public void setSampleFeatures(String sampleFeatures) { this.sampleFeatures = sampleFeatures; }

    public String getFeatureContributions() { return featureContributions; }
    public void setFeatureContributions(String featureContributions) { this.featureContributions = featureContributions; }

   
    public Double getFarmedProbability() { return farmedProbability; }
    public void setFarmedProbability(Double farmedProbability) { this.farmedProbability = farmedProbability; }

    public Double getWildProbability() { return wildProbability; }
    public void setWildProbability(Double wildProbability) { this.wildProbability = wildProbability; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PredictionResultDetailDto that = (PredictionResultDetailDto) o;
        return Objects.equals(id, that.id) &&
               Objects.equals(fishSpeciesId, that.fishSpeciesId) &&
               Objects.equals(fishSpeciesName, that.fishSpeciesName) &&
               Objects.equals(confidenceScore, that.confidenceScore) &&
               Objects.equals(rankOrder, that.rankOrder) &&
               Objects.equals(sampleId, that.sampleId) &&
               Objects.equals(classificationLabel, that.classificationLabel) &&
               Objects.equals(sampleFeatures, that.sampleFeatures) &&
               Objects.equals(featureContributions, that.featureContributions) && // Added
               Objects.equals(farmedProbability, that.farmedProbability) && // Added
               Objects.equals(wildProbability, that.wildProbability);      // Added
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, fishSpeciesId, fishSpeciesName, confidenceScore, rankOrder, sampleId, classificationLabel, sampleFeatures, featureContributions, farmedProbability, wildProbability); // Added new, removed old
    }

    @Override
    public String toString() {
        String featuresSnippet = (sampleFeatures != null ? sampleFeatures.substring(0, Math.min(sampleFeatures.length(), 50)) + "..." : "null");
        return "PredictionResultDetailDto{" +
               "id=" + id +
               ", fishSpeciesId=" + fishSpeciesId +
               ", fishSpeciesName='" + fishSpeciesName + '\'' +
               ", confidenceScore=" + confidenceScore +
               ", rankOrder=" + rankOrder +
               ", sampleId='" + sampleId + '\'' +
               ", classificationLabel='" + classificationLabel + '\'' +
               ", farmedProbability=" + farmedProbability + 
               ", wildProbability=" + wildProbability +   
               ", sampleFeatures='" + featuresSnippet + '\'' +
               ", featureContributions='" + (featureContributions != null ? "present" : "null") + '\'' +
               '}';
    }
}
