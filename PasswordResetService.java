/var/www/api/fish-api/src/main/java/com/example/fishapi/service/PasswordResetService.java
package com.example.fishapi.service;

import com.example.fishapi.dto.ResetPasswordRequest;

public interface PasswordResetService {

    /**
     * 处理请求密码重置的逻辑
     * @param email 用户邮箱
     */
    void handlePasswordResetRequest(String email);

    /**
     * 验证Token并重置密码
     * @param request 包含Token和新密码的请求
     */
    void resetPassword(ResetPasswordRequest request);

    /**
     * 验证密码重置Token的有效性
     * @param token 重置Token
     * @return 如果有效返回true，否则false
     */
     boolean validatePasswordResetToken(String token); // 可选，如果前端需要单独验证
}