# 创建 EmailVerificationServiceImpl.java 实现
cat << EOF > /var/www/api/fish-api/src/main/java/com/example/fishapi/service/impl/EmailVerificationServiceImpl.java
package com.example.fishapi.service.impl;

import com.example.fishapi.model.EmailVerificationCode;
import com.example.fishapi.repository.EmailVerificationCodeRepository;
import com.example.fishapi.repository.UserRepository; // 注入 UserRepository 检查邮箱是否存在
import com.example.fishapi.service.EmailVerificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom; // 用于生成随机数
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.Random;

@Service
public class EmailVerificationServiceImpl implements EmailVerificationService {

    private static final Logger log = LoggerFactory.getLogger(EmailVerificationServiceImpl.class);
    private static final int CODE_LENGTH = 6; // 验证码长度
    private final Random random = new SecureRandom(); // 安全随机数生成器

    @Autowired
    private EmailVerificationCodeRepository verificationCodeRepository;

    @Autowired
    private UserRepository userRepository; // 注入用户仓库

    @Autowired
    private JavaMailSender mailSender; // 邮件发送器

    @Override
    @Transactional
    public void generateAndSendVerificationCode(String email) {
        // 检查邮箱是否存在于用户表中（可选，但推荐）
        if (!userRepository.existsByEmail(email)) {
             log.warn("Verification code requested for non-existent email: {}", email);
             // 即使邮箱不存在，也伪装成功，避免信息泄露
             return;
        }

        // 删除该邮箱可能存在的旧验证码
        verificationCodeRepository.deleteAllByEmail(email);

        // 生成验证码
        String code = generateRandomCode(CODE_LENGTH);

        // 创建并保存验证码实体
        EmailVerificationCode verificationCode = new EmailVerificationCode(email, code);
        verificationCodeRepository.save(verificationCode);
        log.info("Generated verification code for email: {}", email);

        // 发送包含验证码的邮件
        sendVerificationCodeEmail(email, code);
    }

    @Override
    @Transactional // 读取和删除操作在一个事务中
    public boolean verifyCode(String email, String code) {
        LocalDateTime now = LocalDateTime.now();
        Optional<EmailVerificationCode> verificationCodeOpt = verificationCodeRepository
                .findFirstByEmailAndExpiryTimeAfterOrderByExpiryTimeDesc(email, now);

        if (verificationCodeOpt.isPresent()) {
            EmailVerificationCode verificationCode = verificationCodeOpt.get();
            if (verificationCode.getCode().equals(code)) {
                // 验证成功，删除该验证码
                verificationCodeRepository.delete(verificationCode);
                log.info("Verification code verified successfully for email: {}", email);
                return true;
            } else {
                log.warn("Incorrect verification code provided for email: {}", email);
            }
        } else {
            log.warn("No valid verification code found or code expired for email: {}", email);
        }
        return false;
    }

    // --- Helper Methods ---

    private String generateRandomCode(int length) {
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < length; i++) {
            code.append(random.nextInt(10)); // 生成0-9的随机数字
        }
        return code.toString();
    }

    private void sendVerificationCodeEmail(String email, String code) {
        String emailBody = "来自Fishapp,您的验证码是: " + code + "\n验证码将在10分钟内失效。请勿泄露给他人。";

        SimpleMailMessage message = new SimpleMailMessage();
        message.setTo(email);
        message.setSubject("Fish - 邮箱验证码"); 
        message.setText(emailBody);
        message.setFrom("<EMAIL>"); 

        try {
            log.debug("Attempting to send verification code email to {}", email);
            mailSender.send(message);
            log.info("Successfully sent verification code email to {}", email);
        } catch (Exception e) {
            log.error("Error sending verification code email to {}: {}", email, e.getMessage(), e);
            // 这里应该向上抛出异常，让 Controller 知道发送失败，并返回错误给用户
        }
    }
}
EOF
echo "EmailVerificationServiceImpl.java created."
