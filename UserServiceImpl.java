package com.example.fishapi.service.impl;

import com.example.fishapi.dto.ForgotPasswordRequest;
import com.example.fishapi.dto.ResetPasswordRequest;
import com.example.fishapi.dto.SendCodeRequest;
import com.example.fishapi.exception.ResourceNotFoundException;
import com.example.fishapi.model.EmailVerificationCode;
import com.example.fishapi.model.User;
import com.example.fishapi.repository.EmailVerificationCodeRepository;
import com.example.fishapi.repository.UserRepository;
import com.example.fishapi.service.EmailService;
import com.example.fishapi.service.PredictionTaskService; 
import com.example.fishapi.service.UserService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.core.Authentication; 
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.security.authentication.BadCredentialsException;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.Optional;

@Service
public class UserServiceImpl implements UserService {

    private static final Logger log = LoggerFactory.getLogger(UserServiceImpl.class);
    private static final int CODE_EXPIRY_MINUTES = 5;

    private final UserRepository userRepository; 
    private final PasswordEncoder passwordEncoder;
    private final EmailService emailService;
    private final EmailVerificationCodeRepository verificationCodeRepository;
    private final PredictionTaskService predictionTaskService; 

    // *** 使用构造函数注入所有依赖 ***
    @Autowired
    public UserServiceImpl(UserRepository userRepository,
                           PasswordEncoder passwordEncoder,
                           EmailService emailService,
                           EmailVerificationCodeRepository verificationCodeRepository,
                           PredictionTaskService predictionTaskService) { // *** 添加到构造函数 ***
        this.userRepository = userRepository;
        this.passwordEncoder = passwordEncoder;
        this.emailService = emailService;
        this.verificationCodeRepository = verificationCodeRepository;
        this.predictionTaskService = predictionTaskService; // *** 注入 PredictionTaskService ***
    }

    // initiatePasswordReset, sendRegistrationVerificationCode, verifyRegistrationCode, resetPassword 方法
    @Override
    @Transactional
    public void initiatePasswordReset(ForgotPasswordRequest request) {
        String email = request.getEmail();
        Optional<User> userOpt = userRepository.findByEmail(email);
        if (userOpt.isEmpty()) {
             log.warn("Password reset requested for non-existent email: {}", email);
             return;
        }
        User user = userOpt.get();
        if (user.getStatus() != User.UserStatus.ACTIVE) {
            log.warn("Password reset attempt for inactive user: {}", email);
            return;
        }
        sendVerificationCodeInternal(email, "reset");
        log.info("Password reset initiated for email: {}", email);
    }

    @Override
    @Transactional
    public void sendRegistrationVerificationCode(SendCodeRequest request) {
        String email = request.getEmail();
        if (userRepository.existsByEmail(email)) {
            log.warn("Registration verification code requested for already registered email: {}", email);
            return;
        }
        sendVerificationCodeInternal(email, "registration");
        log.info("Registration verification code sent to email: {}", email);
    }

    private void sendVerificationCodeInternal(String email, String type) {
        verificationCodeRepository.deleteByEmail(email);
        log.debug("Deleted existing verification codes for email: {}", email);
        String code = generateVerificationCode();
        LocalDateTime expiryTime = LocalDateTime.now().plus(CODE_EXPIRY_MINUTES, ChronoUnit.MINUTES);
        EmailVerificationCode verificationCode = new EmailVerificationCode(email, code, expiryTime);
        verificationCodeRepository.save(verificationCode);
        log.info("Generated and saved {} verification code for email: {}", type, email);
        emailService.sendVerificationCode(email, code);
    }

    @Override
    @Transactional
    public boolean verifyRegistrationCode(String email, String code) {
        Optional<EmailVerificationCode> verificationRecordOpt = verificationCodeRepository
                .findByEmailAndCodeAndExpiryTimeAfter(email, code, LocalDateTime.now());
        if (verificationRecordOpt.isPresent()) {
            verificationCodeRepository.delete(verificationRecordOpt.get());
            log.info("Registration code verified successfully for email: {}", email);
            return true;
        } else {
            log.warn("Invalid or expired registration code attempt for email: {}", email);
            return false;
        }
    }

    @Override
    @Transactional
    public void resetPassword(ResetPasswordRequest request) {
        String email = request.getEmail();
        String code = request.getCode();
        String newPassword = request.getNewPassword();
        EmailVerificationCode verificationRecord = verificationCodeRepository
                .findByEmailAndCodeAndExpiryTimeAfter(email, code, LocalDateTime.now())
                .orElseThrow(() -> new BadCredentialsException("Invalid or expired verification code."));
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with email: " + email));
        if (passwordEncoder.matches(newPassword, user.getPasswordHash())) {
             throw new IllegalArgumentException("New password cannot be the same as the old password.");
        }
        user.setPasswordHash(passwordEncoder.encode(newPassword));
        user.setUpdatedAt(new Date());
        userRepository.save(user);
        verificationCodeRepository.delete(verificationRecord);
        log.info("Password reset successfully for email: {} and verification code deleted.", email);
    }

    // generateVerificationCode 和 cleanupExpiredVerificationCodes 方法保持不变 
    private String generateVerificationCode() {
        SecureRandom random = new SecureRandom();
        int num = random.nextInt(900000) + 100000;
        return String.valueOf(num);
    }

    @Scheduled(cron = "0 5 * * * ?")
    @Transactional
    public void cleanupExpiredVerificationCodes() {
        LocalDateTime now = LocalDateTime.now();
        log.info("Running cleanup task for expired verification codes older than {}", now);
        long deletedCount = verificationCodeRepository.deleteByExpiryTimeBefore(now);
        if (deletedCount > 0) {
             log.info("Deleted {} expired verification codes.", deletedCount);
        } else {
             log.info("No expired verification codes found to delete.");
        }
    }


    // *** 实现删除用户的方法 ***
    @Override
    @Transactional
    public void deleteUser(Long userIdToDelete, Authentication principal) {
        if (principal == null) {
            throw new SecurityException("无法确定执行操作的用户身份");
        }

        String currentUsername = principal.getName();
        boolean isAdmin = principal.getAuthorities().stream()
                .anyMatch(grantedAuthority -> grantedAuthority.getAuthority().equals("ROLE_ADMIN"));

        log.info("用户 '{}' (管理员: {}) 尝试删除用户 ID: {}", currentUsername, isAdmin, userIdToDelete);

        // 1. 查找要删除的用户
        User userToDelete = userRepository.findById(userIdToDelete)
                .orElseThrow(() -> new ResourceNotFoundException("未找到要删除的用户: " + userIdToDelete));

        // 2. 权限检查
        boolean isSelf = userToDelete.getUsername().equals(currentUsername);
        if (!isSelf && !isAdmin) {
            log.warn("权限不足：用户 '{}' 尝试删除用户 '{}'({})", currentUsername, userToDelete.getUsername(), userIdToDelete);
            throw new SecurityException("无权删除该用户账户");
        }

        log.info("权限检查通过：用户 '{}' {} 删除用户 '{}'({})",
                 currentUsername, (isSelf ? "正在删除自己的账户" : "作为管理员正在"),
                 userToDelete.getUsername(), userIdToDelete);

        // 3. 调用 PredictionTaskService 清理关联任务和文件
        try {
            log.info("准备调用 predictionTaskService.deleteAllUserPredictionTasks 清理用户 {} 的任务...", userToDelete.getUsername());
            int deletedTasksCount = predictionTaskService.deleteAllUserPredictionTasks(userToDelete.getUsername());
            log.info("为用户 {} 清理了 {} 个预测任务及其关联数据/文件。", userToDelete.getUsername(), deletedTasksCount);
        } catch (Exception e) {
            // 处理任务清理失败！
            log.error("调用 deleteAllUserPredictionTasks 清理用户 {} 的任务时出错，但将继续尝试删除用户记录: {}",
                      userToDelete.getUsername(), e.getMessage(), e);
        }

        // 4. 删除用户记录 (JPA 级联可能不再需要，因为任务已手动删除，但保留以防万一)
        userRepository.delete(userToDelete);
        log.info("用户 '{}'({}) 的数据库记录已删除。", userToDelete.getUsername(), userIdToDelete);

        // 5. 清理该用户的验证码记录
        verificationCodeRepository.deleteByEmail(userToDelete.getEmail());
        log.debug("已清理用户 {} 的邮箱验证码记录。", userIdToDelete);
    }
}