# 修改 PasswordResetServiceImpl.java 实现
cat << EOF > /var/www/api/fish-api/src/main/java/com/example/fishapi/service/impl/PasswordResetServiceImpl.java
package com.example.fishapi.service.impl;

import com.example.fishapi.dto.ResetPasswordRequest;
import com.example.fishapi.exception.InvalidTokenException;
import com.example.fishapi.exception.ResourceNotFoundException; 
import com.example.fishapi.model.PasswordResetToken;
import com.example.fishapi.model.User;
import com.example.fishapi.repository.PasswordResetTokenRepository;
import com.example.fishapi.repository.UserRepository;
import com.example.fishapi.service.PasswordResetService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import java.util.Date;
import java.util.Optional;

@Service
public class PasswordResetServiceImpl implements PasswordResetService {

    private static final Logger log = LoggerFactory.getLogger(PasswordResetServiceImpl.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordResetTokenRepository tokenRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private JavaMailSender mailSender;

    @PersistenceContext
    private EntityManager entityManager;

    // --- 方法已重命名 ---
    @Override
    @Transactional
    public void generateAndSendResetToken(String email) {
        // 验证码验证通过后调用
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> {
                     // 找不到用户，流程错误
                     log.error("User not found for email {} during reset token generation after verification.", email);
                     return new ResourceNotFoundException("User not found with email: " + email);
                });

        // 如果用户已有Token，先删除旧的，并立即刷新
        tokenRepository.findByUser(user).ifPresent(token -> {
            log.debug("Deleting existing password reset token for user {}", user.getId());
            tokenRepository.delete(token);
            entityManager.flush();
            log.debug("Flushed session after deleting token.");
        });


        // 创建新的Token
        log.debug("Creating new password reset token for user {}", user.getId());
        PasswordResetToken resetToken = new PasswordResetToken(user);
        try {
             tokenRepository.save(resetToken);
             log.debug("Saved new password reset token with token value: {}", resetToken.getToken());

            // 发送包含重置链接的邮件
            sendPasswordResetLinkEmail(user, resetToken.getToken()); 

        } catch (org.springframework.dao.DataIntegrityViolationException e) {
            log.error("Data integrity violation while saving password reset token for user {}. Error: {}", user.getId(), e.getMessage());
             throw e;
        } catch (Exception e) {
             log.error("Unexpected error saving password reset token for user {}: {}", user.getId(), e.getMessage(), e);
             throw e;
        }
    }

    @Override
    @Transactional
    public void resetPassword(ResetPasswordRequest request) {
        PasswordResetToken resetToken = tokenRepository.findByToken(request.getToken())
                .orElseThrow(() -> new InvalidTokenException("Invalid password reset token"));

        if (resetToken.isExpired()) {
            tokenRepository.delete(resetToken);
            throw new InvalidTokenException("Password reset token has expired");
        }

        User user = resetToken.getUser();

        user.setPasswordHash(passwordEncoder.encode(request.getNewPassword()));
        user.setUpdatedAt(new Date());
        userRepository.save(user);

        tokenRepository.delete(resetToken);

        log.info("Password reset successfully for user: {}", user.getUsername());
    }

    @Override
    public boolean validatePasswordResetToken(String token) {
         Optional<PasswordResetToken> tokenOpt = tokenRepository.findByToken(token);
         if(tokenOpt.isEmpty()){
             return false;
         }
         PasswordResetToken resetToken = tokenOpt.get();
         if(resetToken.isExpired()){
            tokenRepository.delete(resetToken);
            return false;
         }
         return true;
    }


    // --- Helper Methods ---
    private void sendPasswordResetLinkEmail(User user, String token) {
        String resetUrl = "https://www.cjfish.com/reset-password?token=" + token; 
        String emailBody = "来自Fishapp,尊敬的用户：您好，请点击以下链接重置您的密码: " + resetUrl + "\n链接将在60分钟内失效。";

        SimpleMailMessage message = new SimpleMailMessage();
        message.setTo(user.getEmail());
        message.setSubject("Fish - 密码重置请求"); // 可以保持不变或修改
        message.setText(emailBody);
        message.setFrom("<EMAIL>");

        try {
            log.debug("Attempting to send password reset link email to {}", user.getEmail());
            mailSender.send(message);
            log.info("Successfully sent password reset link email to {}", user.getEmail());
            log.info("Password reset token generated for user: {}", user.getUsername()); // 日志现在放在成功发送后
        } catch (Exception e) {
            log.error("Error sending password reset link email to {}: {}", user.getEmail(), e.getMessage(), e);
            // 考虑向上抛出异常
            // throw new RuntimeException("Failed to send password reset link email for user " + user.getEmail(), e);
        }
    }
}
EOF
echo "PasswordResetServiceImpl.java updated (method renamed, user lookup adjusted)."