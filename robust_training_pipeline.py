#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
鲁棒训练与评估流水线 - V4 简化版
Robust Training and Evaluation Pipeline - V4 Simplified

该脚本是整个流程的最后一步，负责加载由前续步骤准备好的数据，
并使用TabPFN模型进行训练、评估和深入分析。

核心功能:
1. 加载预先划分好的训练、验证和测试数据集（原始丰度）。
2. 加载由LGBM-RFE优化后的最终特征集。
3. 使用TabPFN进行模型训练。
4. 生成全面的性能评估指标和可视化图表，包括：
   - ROC曲线与最佳阈值
   - 统一的混淆矩阵（训练、验证、测试集）
   - 特征重要性（排列法）
   - 学习曲线
   - SHAP分析（摘要、依赖、瀑布图和力图）
5. 执行鲁棒性验证测试。
"""
# --- 基础库 ---
import os
import sys
import json
import argparse
import traceback
import warnings
from datetime import datetime
from typing import Dict, List, Tuple

# --- 数据处理 ---
import pandas as pd
import numpy as np
from scipy.stats import gmean
from scipy import stats
from sklearn.model_selection import (train_test_split, cross_validate, StratifiedKFold, 
                                     RepeatedStratifiedKFold, learning_curve)
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.metrics import (make_scorer, f1_score, accuracy_score, precision_score, recall_score, 
                            roc_curve, auc, confusion_matrix, classification_report, roc_auc_score)

# --- 模型与解释性 ---
import shap
from sklearn.inspection import permutation_importance

# --- 可视化 ---
import matplotlib
matplotlib.use('Agg') # 设置为非交互式后端，以避免在脚本中出现tkinter错误
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns
from matplotlib.backends.backend_pdf import PdfPages

# --- 序列化 ---
from joblib import dump, load

# --- 动态导入与安装 torch 和 tabpfn ---
try:
    import torch
except ImportError:
    print("torch模块未安装，正在尝试自动安装...")
    import subprocess
    # 使用国内镜像源加速
    subprocess.check_call([sys.executable, "-m", "pip", "install", "--no-cache-dir", "torch", "torchvision", "torchaudio", "-i", "https://pypi.tuna.tsinghua.edu.cn/simple"])
    try:
        import torch
    except ImportError:
        print("错误: 安装torch后仍然无法导入。请手动检查并安装PyTorch。")
        sys.exit(1)

try:
    from tabpfn import TabPFNClassifier
    TABPFN_AVAILABLE = True
except ImportError:
    print("TabPFN模块未安装，正在尝试自动安装...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "--no-cache-dir", "tabpfn"])
    try:
        from tabpfn import TabPFNClassifier
        TABPFN_AVAILABLE = True
    except ImportError:
        print("错误: TabPFN安装后仍然无法导入。请检查Python环境和pip安装。")
        TABPFN_AVAILABLE = False
        sys.exit(1)

# --- 全局常量与配置 ---
SPECIES_MAP = {'鳙鱼': 'Bighead Carp', '鲢鱼': 'Silver Carp'}
CLASS_LABEL_MAP = {'养殖': 'Culture', '野生': 'Wild'}
SPECIES_PINYIN_MAP = {'鲢鱼': 'lianyu', '鳙鱼': 'yongyu'}

# 配置Matplotlib字体
plt.rcParams['font.family'] = 'serif'
plt.rcParams['font.serif'] = ['Times New Roman'] 
plt.rcParams['axes.unicode_minus'] = True
plt.rcParams['pdf.fonttype'] = 42
plt.rcParams['svg.fonttype'] = 'none'

# --- 辅助函数 ---

def _save_fig_to_formats(fig, output_path_without_extension: str):
    """将matplotlib图表同时保存为PNG和PDF格式"""
    png_path = f"{output_path_without_extension}.png"
    pdf_path = f"{output_path_without_extension}.pdf"
    
    # 确保目录存在
    ensure_dir_exists(png_path)
    
    # 保存为PNG
    fig.savefig(png_path, dpi=300, bbox_inches='tight')
    
    # 保存为PDF
    fig.savefig(pdf_path, bbox_inches='tight')
    plt.close(fig) # 保存后关闭图表以释放内存

def _sanitize_filename(filename: str) -> str:
    """清理文件名中的非法字符"""
    return "".join([c for c in filename if c.isalpha() or c.isdigit() or c in (' ', '_', '-')]).rstrip()

def ensure_dir_exists(file_path: str) -> str:
    """确保文件路径的目录存在"""
    try:
        directory = os.path.dirname(file_path)
        if directory and not os.path.exists(directory):
            print(f"创建目录: {directory}")
            os.makedirs(directory, exist_ok=True)
        return file_path
    except Exception as e:
        print(f"创建目录时出错 '{directory}': {str(e)}")
        raise

def check_cuda_availability() -> str:
    """检查CUDA是否可用并返回适当的设备类型"""
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        print(f"检测到CUDA，将使用GPU: {gpu_name}")
        torch.cuda.empty_cache()
        return "cuda"
    else:
        print("未检测到CUDA，将使用CPU")
        return "cpu"

def save_metrics_to_text(metrics: Dict, output_dir: str, current_species: str, train_metrics: Dict = None, optimal_thresholds: Dict = None, test_metrics: Dict = None):
    """将模型性能指标保存为文本文件"""
    print("保存性能指标到文本文件...")
    eng_species = SPECIES_MAP.get(current_species, current_species)
    pinyin_species = SPECIES_PINYIN_MAP.get(current_species, current_species)
    output_path = ensure_dir_exists(os.path.join(output_dir, f"{pinyin_species}_model_metrics.txt"))
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(f"======== {eng_species} Model Performance Evaluation ========\n")
        f.write(f"Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        if train_metrics:
            f.write("== Training Set Performance ==\n")
            for model_name, model_metrics in train_metrics.items():
                f.write(f"\n{model_name}:\n" + "".join([f"  {k.capitalize()}: {v:.4f}\n" for k, v in model_metrics.items() if 'cv' not in k]))

        f.write("\n== Validation Set Performance ==\n")
        for model_name, model_metrics in metrics.items():
            f.write(f"\n{model_name}:\n" + "".join([f"  {k.capitalize()}: {v:.4f}\n" for k, v in model_metrics.items() if 'cv' not in k]))

        if optimal_thresholds:
            f.write("\n\n== Optimal Prediction Threshold (based on Youden's J) ==\n")
            for model_name, threshold in optimal_thresholds.items():
                f.write(f"  {model_name}: {threshold:.4f}\n")

        if test_metrics:
            f.write("\n\n== Final Test Set Performance (Held-out 10%) ==\n")
            f.write(f"  Accuracy: {test_metrics['accuracy']:.4f}\n")
            f.write(f"  Precision: {test_metrics['precision']:.4f}\n")
            f.write(f"  Recall: {test_metrics['recall']:.4f}\n")
            f.write(f"  F1 Score: {test_metrics['f1']:.4f}\n")
    
    print(f"性能指标已保存到 {output_path}")

def plot_roc_curves(models: Dict, X_val: np.ndarray, y_val: np.ndarray, output_dir: str, current_species: str) -> Tuple[plt.Figure, Dict]:
    """绘制ROC曲线并计算最佳阈值"""
    print("绘制ROC曲线...")
    eng_species = SPECIES_MAP.get(current_species, current_species)
    pinyin_species = SPECIES_PINYIN_MAP.get(current_species, current_species)
    fig = plt.figure(figsize=(12, 8))
    
    optimal_thresholds = {}
    
    for name, model in models.items():
        y_prob = model.predict_proba(X_val)[:, 1]
        fpr, tpr, thresholds = roc_curve(y_val, y_prob)
        roc_auc = auc(fpr, tpr)

        # --- 新增: 使用Bootstrap计算AUC的95%置信区间 ---
        n_bootstraps = 1000
        bootstrapped_aucs = []
        rng = np.random.RandomState(42)
        for _ in range(n_bootstraps):
            try:
                indices = rng.randint(0, len(y_prob), len(y_prob))
                if len(np.unique(y_val[indices])) < 2: continue
                fpr_boot, tpr_boot, _ = roc_curve(y_val[indices], y_prob[indices])
                bootstrapped_aucs.append(auc(fpr_boot, tpr_boot))
            except ValueError:
                continue
        
        confidence_lower = np.percentile(bootstrapped_aucs, 2.5)
        confidence_upper = np.percentile(bootstrapped_aucs, 97.5)
        
        j_scores = tpr - fpr
        best_j_score_idx = np.argmax(j_scores)
        optimal_threshold = thresholds[best_j_score_idx]
        optimal_point = (fpr[best_j_score_idx], tpr[best_j_score_idx])
        optimal_thresholds[name] = float(optimal_threshold)
        
        plt.plot(fpr, tpr, lw=2, label=f'{name} (AUC = {roc_auc:.4f} [95% CI: {confidence_lower:.4f}-{confidence_upper:.4f}])')
        plt.scatter(optimal_point[0], optimal_point[1], marker='o', color='red', s=100, zorder=5, label=f'Optimal Threshold: {optimal_threshold:.2f}')
    
    plt.plot([0, 1], [0, 1], color='gray', lw=2, linestyle='--')
    plt.xlim([0.0, 1.0]); plt.ylim([0.0, 1.05])
    plt.xlabel('False Positive Rate', fontsize=12)
    plt.ylabel('True Positive Rate', fontsize=12)
    plt.title(f'{eng_species} - ROC Curve with Confidence Interval and Optimal Threshold', fontsize=14)
    plt.legend(loc="lower right", fontsize=10)
    
    output_path_base = os.path.join(output_dir, f"{pinyin_species}_roc_curves")
    _save_fig_to_formats(fig, output_path_base)
    
    print(f"ROC曲线已保存到 {output_path_base}.[png/pdf]")
    return fig, optimal_thresholds

def plot_all_confusion_matrices(
    y_train: np.ndarray, y_train_pred: np.ndarray,
    y_val: np.ndarray, y_val_pred: np.ndarray,
    y_test: np.ndarray, y_test_pred: np.ndarray,
    class_names: List[str], output_dir: str, model_name: str, current_species: str
):
    """在一个图中绘制训练集、验证集和测试集的混淆矩阵（横向布局）"""
    print(f"绘制 {model_name} 的统一混淆矩阵 (训练、验证、测试)...")
    fig, axs = plt.subplots(1, 3, figsize=(30, 10)) # 增加图形尺寸

    eng_species = SPECIES_MAP.get(current_species, current_species)
    pinyin_species = SPECIES_PINYIN_MAP.get(current_species, current_species)
    # 使用简洁的类别标签，避免重复信息
    # 将类别名称中的物种信息简化
    simplified_class_names = []
    for name in class_names:
        # 移除类别名称中的重复物种信息
        if eng_species.lower().replace(' ', '_') in name.lower():
            # 如果类别名称已包含物种信息，只保留环境类型
            if 'culture' in name.lower():
                simplified_name = 'Cultured'
            elif 'wild' in name.lower():
                simplified_name = 'Wild'
            else:
                simplified_name = name.split('_')[0].title()  # 取第一部分并首字母大写
        else:
            simplified_name = name.title()
        simplified_class_names.append(simplified_name)

    datasets = {
        'Training Set': (y_train, y_train_pred, axs[0]),
        'Validation Set': (y_val, y_val_pred, axs[1]),
        'Test Set': (y_test, y_test_pred, axs[2])
    }

    # 使用更高对比度的颜色映射
    cmap = "Blues"

    for title, (y_true, y_pred, ax) in datasets.items():
        cm = confusion_matrix(y_true, y_pred)
        acc = accuracy_score(y_true, y_pred)

        # 修复数字显示问题：增加字体大小，设置颜色，确保可见性
        sns.heatmap(cm, annot=True, fmt='d', cmap=cmap,
                    xticklabels=simplified_class_names, yticklabels=simplified_class_names,
                    ax=ax,
                    annot_kws={"size": 20, "weight": "bold", "color": "black"},
                    cbar_kws={"shrink": 0.8},
                    linewidths=2, linecolor='white')

        # 设置标题和标签
        ax.set_title(f'{model_name} - {title}\n(Accuracy: {acc:.3f})', fontsize=20, pad=20)
        ax.set_ylabel('True Label', fontsize=16, weight='bold')
        ax.set_xlabel('Predicted Label', fontsize=16, weight='bold')

        # 调整刻度标签
        ax.tick_params(axis='both', which='major', labelsize=14)

        # 在每个单元格中心添加数值标注（双重保险）
        for i in range(cm.shape[0]):
            for j in range(cm.shape[1]):
                ax.text(j + 0.5, i + 0.5, str(cm[i, j]),
                       ha="center", va="center",
                       fontsize=20, weight="bold", color="black")

    plt.suptitle(f'Confusion Matrix Comparison for {eng_species}', fontsize=24, y=0.98, weight='bold')
    fig.tight_layout(rect=[0, 0, 1, 0.94]) # 调整布局

    output_filename_base = os.path.join(output_dir, f"{pinyin_species}_confusion_matrices_comparison")
    _save_fig_to_formats(fig, output_filename_base)

    print(f"统一混淆矩阵图已保存到 {output_filename_base}.[png/pdf]")

def plot_tabpfn_feature_importance(model, X_val, y_val, features, output_dir, current_species):
    """通过排列重要性分析TabPFN模型的特征重要性"""
    print("计算TabPFN特征重要性 (排列法，基于AUC)...")
    eng_species = SPECIES_MAP.get(current_species, current_species)

    result = permutation_importance(model, X_val, y_val, n_repeats=10, random_state=42, n_jobs=1, scoring='roc_auc')
    importance = result.importances_mean

    # --- [核心修复] 裁剪非正值以兼容对数刻度 ---
    importance = np.maximum(importance, 1e-9)

    indices = np.argsort(importance)[::-1] # 改为降序，最重要的在顶部

    fig = plt.figure(figsize=(12, 8))
    plt.barh(range(len(indices)), importance[indices], align='center', color='purple', alpha=0.7)
    plt.yticks(range(len(indices)), [features[i] for i in indices], fontsize=10)
    plt.xlabel('Feature Importance (Permutation)', fontsize=12)
    plt.title(f'TabPFN Feature Importance for {eng_species}', fontsize=14)
    for i, v in enumerate(importance[indices]):
        plt.text(v, i, f' {v:.4f}', va='center', color='black')
    plt.xscale('log')
    plt.grid(True, axis='x', alpha=0.3); plt.tight_layout()

    output_path_base = os.path.join(output_dir, f"{current_species}_tabpfn_feature_importance")
    _save_fig_to_formats(fig, output_path_base)

    print(f"TabPFN特征重要性图已保存到 {output_path_base}.[png/pdf]")
    return importance

def plot_learning_curves(model, X_train, y_train, output_dir, current_species):
    """
    绘制学习曲线来评估模型性能随训练样本数量的变化。
    """
    print("绘制学习曲线...")
    eng_species = SPECIES_MAP.get(current_species, current_species)
    pinyin_species = SPECIES_PINYIN_MAP.get(current_species, current_species)
    
    cv_strategy = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    
    train_sizes, train_scores, val_scores = learning_curve(
        model,
        X_train,
        y_train,
        cv=cv_strategy,
        n_jobs=1,
        train_sizes=np.linspace(0.1, 1.0, 10),
        scoring='accuracy'
    )
    
    train_scores_mean = np.mean(train_scores, axis=1)
    train_scores_std = np.std(train_scores, axis=1)
    val_scores_mean = np.mean(val_scores, axis=1)
    val_scores_std = np.std(val_scores, axis=1)
    
    fig = plt.figure(figsize=(10, 6))
    plt.grid()
    
    plt.fill_between(train_sizes, train_scores_mean - train_scores_std,
                     train_scores_mean + train_scores_std, alpha=0.1,
                     color="r")
    plt.fill_between(train_sizes, val_scores_mean - val_scores_std,
                     val_scores_mean + val_scores_std, alpha=0.1, color="g")
    
    plt.plot(train_sizes, train_scores_mean, 'o-', color="r",
             label="Training score")
    plt.plot(train_sizes, val_scores_mean, 'o-', color="g",
             label="Cross-validation score")
             
    plt.legend(loc="best")
    plt.xlabel("Training examples")
    plt.ylabel("Accuracy Score")
    plt.title(f'Learning Curve for TabPFN ({eng_species})')
    plt.ylim(0.7, 1.01)
    
    output_path_base = os.path.join(output_dir, f"{pinyin_species}_learning_curve")
    _save_fig_to_formats(fig, output_path_base)

    print(f"学习曲线已保存到: {output_path_base}.[png/pdf]")

def plot_tabpfn_shap_analysis(model, X_train_df, X_val_df, y_val, features, class_names, output_dir, current_species="鳙鱼"):
    """
    执行SHAP分析并为每个图表保存独立的PNG和PDF文件。
    针对TabPFN模型进行了特殊优化。
    """
    print("执行TabPFN专用SHAP分析...")
    eng_species = SPECIES_MAP.get(current_species, current_species)
    pinyin_species = SPECIES_PINYIN_MAP.get(current_species, current_species)
    target_class_index = 1 # 假设我们总是关注第二个类别（如 '野生'）
    target_class_name = class_names[target_class_index]

    # 为TabPFN创建兼容的预测函数
    def tabpfn_predict_wrapper(X):
        """TabPFN预测包装器，确保输入格式正确"""
        if isinstance(X, np.ndarray):
            X_df = pd.DataFrame(X, columns=features)
        else:
            X_df = X
        return model.predict_proba(X_df)

    def tabpfn_predict_single_class(X):
        """返回目标类别的预测概率"""
        proba = tabpfn_predict_wrapper(X)
        return proba[:, target_class_index]

    print(f"  - 创建SHAP解释器 (背景数据: {min(100, len(X_train_df))} 样本)...")

    # 使用较小的背景数据集以提高计算效率
    background_data = X_train_df.iloc[:min(100, len(X_train_df))]

    try:
        # 方法1: 尝试使用Explainer
        explainer = shap.Explainer(tabpfn_predict_wrapper, background_data)
        shap_values = explainer(X_val_df.iloc[:min(50, len(X_val_df))])  # 限制验证样本数量

        # 确保我们使用的是目标类别的SHAP值
        if len(shap_values.shape) == 3:  # 多类别情况
            shap_values_target = shap_values[..., target_class_index]
        else:  # 二分类情况
            shap_values_target = shap_values

        print(f"  - SHAP值计算成功，形状: {shap_values_target.shape}")

    except Exception as e1:
        print(f"  - 方法1失败，尝试KernelExplainer: {str(e1)}")
        try:
            # 方法2: 使用KernelExplainer
            explainer = shap.KernelExplainer(tabpfn_predict_single_class, background_data.values)
            shap_values_raw = explainer.shap_values(X_val_df.iloc[:min(20, len(X_val_df))].values)

            # 创建Explanation对象
            shap_values_target = shap.Explanation(
                values=shap_values_raw,
                base_values=explainer.expected_value,
                data=X_val_df.iloc[:min(20, len(X_val_df))].values,
                feature_names=features
            )

            print(f"  - KernelExplainer计算成功，形状: {shap_values_target.values.shape}")

        except Exception as e2:
            print(f"  - 方法2也失败，使用LinearExplainer: {str(e2)}")
            try:
                # 方法3: 使用LinearExplainer (适用于线性模型近似)
                explainer = shap.LinearExplainer(tabpfn_predict_single_class, background_data.values)
                shap_values_raw = explainer.shap_values(X_val_df.iloc[:min(30, len(X_val_df))].values)

                # 创建Explanation对象
                shap_values_target = shap.Explanation(
                    values=shap_values_raw,
                    base_values=explainer.expected_value,
                    data=X_val_df.iloc[:min(30, len(X_val_df))].values,
                    feature_names=features
                )

                print(f"  - LinearExplainer计算成功，形状: {shap_values_target.values.shape}")

            except Exception as e3:
                print(f"  - 所有SHAP方法都失败: {str(e3)}")
                print("  - 跳过SHAP分析")
                return

    # --- 1. SHAP Beeswarm Plot ---
    fig_beeswarm = plt.figure(figsize=(10, 8))
    shap.plots.beeswarm(shap_values_target, max_display=20, show=False)
    plt.title(f"SHAP Beeswarm Plot ({eng_species} - Impact on '{target_class_name}')", fontsize=16)
    plt.tight_layout()
    beeswarm_path_base = os.path.join(output_dir, f"{pinyin_species}_shap_beeswarm")
    _save_fig_to_formats(fig_beeswarm, beeswarm_path_base)
    print(f"SHAP Beeswarm图已保存到: {beeswarm_path_base}.[png/pdf]")

    # --- 2. SHAP Bar Plot (Global Importance) ---
    fig_bar = plt.figure(figsize=(10, 8))
    shap.plots.bar(shap_values_target, max_display=20, show=False)
    plt.title(f"Mean Absolute SHAP Value ({eng_species} - Impact on '{target_class_name}')", fontsize=16)
    plt.tight_layout()
    bar_path_base = os.path.join(output_dir, f"{pinyin_species}_shap_bar")
    _save_fig_to_formats(fig_bar, bar_path_base)
    print(f"SHAP Bar图已保存到: {bar_path_base}.[png/pdf]")

    # --- 3. SHAP Waterfall Plots for Representative Samples ---
    print("生成SHAP瀑布图...")
    # 选择几个代表性样本进行瀑布图分析
    n_samples = min(5, len(X_val_df))  # 最多选择5个样本
    sample_indices = np.linspace(0, len(X_val_df)-1, n_samples, dtype=int)

    for i, sample_idx in enumerate(sample_indices):
        try:
            fig_waterfall = plt.figure(figsize=(12, 8))

            # 创建瀑布图
            shap.plots.waterfall(shap_values_target[sample_idx], max_display=15, show=False)

            # 获取样本的真实标签和预测标签
            true_label = class_names[y_val.iloc[sample_idx]] if hasattr(y_val, 'iloc') else class_names[y_val[sample_idx]]
            pred_proba = model.predict_proba(X_val_df.iloc[[sample_idx]])[0, target_class_index]

            plt.title(f"SHAP Waterfall Plot - Sample {i+1} ({eng_species})\n"
                     f"True: {true_label}, Pred Prob({target_class_name}): {pred_proba:.3f}",
                     fontsize=14, pad=20)
            plt.tight_layout()

            waterfall_path_base = os.path.join(output_dir, f"{pinyin_species}_shap_waterfall_sample_{i+1}")
            _save_fig_to_formats(fig_waterfall, waterfall_path_base)
            print(f"  - 样本 {i+1} 的瀑布图已保存到: {waterfall_path_base}.[png/pdf]")

        except Exception as e:
            print(f"  - 警告: 样本 {i+1} 的瀑布图生成失败: {str(e)}")
            plt.close()

    # --- 4. SHAP Force Plots for Representative Samples ---
    print("生成SHAP力图...")
    try:
        # 选择几个代表性样本进行力图分析
        for i, sample_idx in enumerate(sample_indices[:3]):  # 限制为前3个样本以避免过多图表
            try:
                # 获取样本信息
                true_label = class_names[y_val.iloc[sample_idx]] if hasattr(y_val, 'iloc') else class_names[y_val[sample_idx]]
                pred_proba = model.predict_proba(X_val_df.iloc[[sample_idx]])[0, target_class_index]

                # 直接使用改进的条形图方法生成力图
                fig_force, ax = plt.subplots(figsize=(16, 10))

                # 获取该样本的SHAP值
                sample_shap = shap_values_target[sample_idx].values
                feature_names = shap_values_target.feature_names

                # 按SHAP值绝对值排序，只显示前20个最重要的特征
                abs_shap = np.abs(sample_shap)
                top_indices = np.argsort(abs_shap)[-20:]
                top_shap = sample_shap[top_indices]
                top_features = [feature_names[i] for i in top_indices]

                # 创建水平条形图
                colors = ['#FF6B6B' if x < 0 else '#4ECDC4' for x in top_shap]
                bars = ax.barh(range(len(top_shap)), top_shap, color=colors, alpha=0.8, edgecolor='black', linewidth=0.5)

                # 设置y轴标签
                ax.set_yticks(range(len(top_features)))
                ax.set_yticklabels(top_features, fontsize=10)

                # 设置x轴标签和标题
                ax.set_xlabel('SHAP Value (Impact on Prediction)', fontsize=12, weight='bold')
                ax.set_title(f"SHAP Force Plot - Sample {i+1} ({eng_species})\n"
                           f"True Label: {true_label} | Predicted Prob({target_class_name}): {pred_proba:.3f}\n"
                           f"Base Value: {shap_values_target[sample_idx].base_values:.3f}",
                           fontsize=14, weight='bold', pad=20)

                # 添加零线
                ax.axvline(x=0, color='black', linestyle='-', linewidth=2, alpha=0.7)

                # 添加网格
                ax.grid(True, alpha=0.3, axis='x')

                # 添加数值标签
                for j, value in enumerate(top_shap):
                    label_x = value + (0.005 if value >= 0 else -0.005)
                    ax.text(label_x, j, f'{value:.3f}',
                           ha='left' if value >= 0 else 'right', va='center',
                           fontsize=9, weight='bold',
                           bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.7))

                # 添加图例
                from matplotlib.patches import Patch
                legend_elements = [
                    Patch(facecolor='#4ECDC4', label='Positive Impact (→ Higher Probability)'),
                    Patch(facecolor='#FF6B6B', label='Negative Impact (→ Lower Probability)')
                ]
                ax.legend(handles=legend_elements, loc='lower right', fontsize=10)

                # 添加统计信息
                total_positive = np.sum(top_shap[top_shap > 0])
                total_negative = np.sum(top_shap[top_shap < 0])
                net_impact = total_positive + total_negative

                info_text = f"Net SHAP Impact: {net_impact:.3f}\n"
                info_text += f"Positive Contributions: {total_positive:.3f}\n"
                info_text += f"Negative Contributions: {total_negative:.3f}"

                ax.text(0.02, 0.98, info_text, transform=ax.transAxes, fontsize=10,
                       bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8),
                       verticalalignment='top')

                plt.tight_layout()

                force_path_base = os.path.join(output_dir, f"{pinyin_species}_shap_force_sample_{i+1}")
                _save_fig_to_formats(fig_force, force_path_base)
                print(f"  - 样本 {i+1} 的力图已保存到: {force_path_base}.[png/pdf]")

            except Exception as e:
                print(f"  - 警告: 样本 {i+1} 的力图生成失败: {str(e)}")
                plt.close()

    except Exception as e:
        print(f"  - 警告: 力图生成过程出现问题: {str(e)}")

    # --- 5. SHAP Dependence Plots for Top Features ---
    print("生成SHAP依赖图...")
    try:
        # 计算全局重要性以确定最重要的特征
        if hasattr(shap_values_target, 'values'):
            mean_abs_shap = np.abs(shap_values_target.values).mean(axis=0)
            feature_names_list = shap_values_target.feature_names
        else:
            mean_abs_shap = np.abs(shap_values_target).mean(axis=0)
            feature_names_list = features

        top_feature_indices = np.argsort(mean_abs_shap)[-6:]  # 选择前6个最重要的特征
        top_features = [feature_names_list[i] for i in top_feature_indices]

        # 获取当前使用的验证数据
        current_val_data = X_val_df.iloc[:len(shap_values_target)]

        for idx, feature_name in enumerate(top_features):
            try:
                fig_dep, ax = plt.subplots(figsize=(12, 8))

                # 获取特征值和对应的SHAP值
                if feature_name in current_val_data.columns:
                    feature_values = current_val_data[feature_name].values

                    if hasattr(shap_values_target, 'values'):
                        if hasattr(shap_values_target, 'feature_names'):
                            feature_idx = list(shap_values_target.feature_names).index(feature_name)
                        else:
                            feature_idx = list(features).index(feature_name)
                        shap_values_feature = shap_values_target.values[:, feature_idx]
                    else:
                        feature_idx = list(features).index(feature_name)
                        shap_values_feature = shap_values_target[:, feature_idx]

                    # 确保数据长度一致
                    min_len = min(len(feature_values), len(shap_values_feature))
                    feature_values = feature_values[:min_len]
                    shap_values_feature = shap_values_feature[:min_len]

                    # 创建散点图
                    scatter = ax.scatter(feature_values, shap_values_feature,
                                       c=feature_values, cmap='viridis',
                                       alpha=0.7, s=60, edgecolors='black', linewidth=0.5)

                    # 添加颜色条
                    cbar = plt.colorbar(scatter, ax=ax)
                    cbar.set_label(f'{feature_name} (Feature Value)', fontsize=12)

                    # 设置标签和标题
                    ax.set_xlabel(f'{feature_name} (Feature Value)', fontsize=12, weight='bold')
                    ax.set_ylabel(f'SHAP Value for {target_class_name}', fontsize=12, weight='bold')
                    ax.set_title(f'SHAP Dependence Plot: {feature_name}\n'
                               f'Impact on {target_class_name} Prediction ({eng_species})',
                               fontsize=14, weight='bold', pad=20)

                    # 添加零线
                    ax.axhline(y=0, color='red', linestyle='--', alpha=0.7, linewidth=2)

                    # 添加网格
                    ax.grid(True, alpha=0.3)

                    # 添加统计信息
                    if len(feature_values) > 1 and len(shap_values_feature) > 1:
                        correlation = np.corrcoef(feature_values, shap_values_feature)[0, 1]
                        mean_shap = np.mean(np.abs(shap_values_feature))

                        info_text = f'Correlation: {correlation:.3f}\n'
                        info_text += f'Mean |SHAP|: {mean_shap:.3f}\n'
                        info_text += f'Samples: {len(feature_values)}'

                        ax.text(0.02, 0.98, info_text,
                               transform=ax.transAxes, fontsize=10,
                               bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                               verticalalignment='top')

                    plt.tight_layout()

                    sanitized_feature_name = _sanitize_filename(feature_name)
                    dep_plot_path_base = os.path.join(output_dir, f"{pinyin_species}_shap_dependence_{sanitized_feature_name}")
                    _save_fig_to_formats(fig_dep, dep_plot_path_base)
                    print(f"  - {feature_name} 的依赖图已保存到: {dep_plot_path_base}.[png/pdf]")

                else:
                    print(f"  - 警告: 特征 {feature_name} 在验证数据中未找到")
                    plt.close()

            except Exception as e:
                print(f"  - 警告: {feature_name} 的依赖图生成失败: {str(e)}")
                plt.close()

    except Exception as e:
        print(f"  - 警告: 依赖图生成过程出现问题: {str(e)}")

    # --- 6. SHAP Summary Plot (Alternative to Beeswarm) ---
    print("生成SHAP总结图...")
    try:
        fig_summary, ax = plt.subplots(figsize=(14, 10))

        # 手动创建总结图
        # 计算特征重要性
        mean_abs_shap = np.abs(shap_values_target.values).mean(axis=0)
        top_indices = np.argsort(mean_abs_shap)[-20:]  # 选择前20个最重要的特征

        # 获取前20个特征的数据
        top_features = [features[i] for i in top_indices]
        top_shap_values = shap_values_target.values[:, top_indices]
        top_feature_values = X_val_df.iloc[:, top_indices].values

        # 为每个特征创建散点图
        y_positions = []
        for i, feature_name in enumerate(top_features):
            # 为每个样本添加一些随机噪声以避免重叠
            y_pos = i + np.random.normal(0, 0.1, len(top_shap_values[:, i]))
            y_positions.extend(y_pos)

            # 创建散点图，颜色表示特征值
            scatter = ax.scatter(top_shap_values[:, i], y_pos,
                               c=top_feature_values[:, i], cmap='coolwarm',
                               alpha=0.6, s=20, edgecolors='black', linewidth=0.1)

        # 设置y轴
        ax.set_yticks(range(len(top_features)))
        ax.set_yticklabels(top_features, fontsize=10)
        ax.set_ylim(-0.5, len(top_features) - 0.5)

        # 设置x轴和标题
        ax.set_xlabel('SHAP Value (Impact on Model Output)', fontsize=12, weight='bold')
        ax.set_title(f'SHAP Summary Plot ({eng_species})\nImpact on {target_class_name} Prediction',
                    fontsize=16, weight='bold', pad=20)

        # 添加零线
        ax.axvline(x=0, color='black', linestyle='-', alpha=0.3, linewidth=1)

        # 添加颜色条
        cbar = plt.colorbar(scatter, ax=ax, shrink=0.8)
        cbar.set_label('Feature Value\n(Low ← → High)', fontsize=11)

        # 添加网格
        ax.grid(True, alpha=0.3, axis='x')

        # 添加说明文本
        ax.text(0.02, 0.98,
               'Each dot represents one sample\nColor indicates feature value\nPosition shows SHAP impact',
               transform=ax.transAxes, fontsize=10,
               bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8),
               verticalalignment='top')

        plt.tight_layout()

        summary_path_base = os.path.join(output_dir, f"{pinyin_species}_shap_summary")
        _save_fig_to_formats(fig_summary, summary_path_base)
        print(f"SHAP总结图已保存到: {summary_path_base}.[png/pdf]")

    except Exception as e:
        print(f"  - 警告: SHAP总结图生成失败: {str(e)}")
        plt.close()

    # --- 7. TabPFN vs SHAP 重要性对比分析 ---
    print("生成TabPFN与SHAP重要性对比分析...")
    try:
        # 获取TabPFN的特征重要性
        print("  - 计算TabPFN排列重要性...")
        result = permutation_importance(model, X_val_df, y_val, n_repeats=5, random_state=42, n_jobs=1, scoring='roc_auc')
        tabpfn_importance = result.importances_mean

        # 获取SHAP的特征重要性
        if hasattr(shap_values_target, 'values'):
            shap_importance = np.abs(shap_values_target.values).mean(axis=0)
            feature_names_list = shap_values_target.feature_names
        else:
            shap_importance = np.abs(shap_values_target).mean(axis=0)
            feature_names_list = features

        # 创建对比图
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(24, 8))

        # 标准化重要性分数以便比较
        tabpfn_norm = tabpfn_importance / np.max(tabpfn_importance)
        shap_norm = shap_importance / np.max(shap_importance)

        # 选择前15个最重要的特征进行显示
        top_features_combined = set()
        top_features_combined.update(np.argsort(tabpfn_importance)[-15:])
        top_features_combined.update(np.argsort(shap_importance)[-15:])
        top_indices = sorted(list(top_features_combined))

        top_feature_names = [feature_names_list[i] for i in top_indices]
        top_tabpfn = tabpfn_norm[top_indices]
        top_shap = shap_norm[top_indices]

        # 子图1: TabPFN重要性
        y_pos = np.arange(len(top_feature_names))
        ax1.barh(y_pos, top_tabpfn, color='skyblue', alpha=0.8, edgecolor='black')
        ax1.set_yticks(y_pos)
        ax1.set_yticklabels(top_feature_names, fontsize=10)
        ax1.set_xlabel('Normalized Importance', fontsize=12)
        ax1.set_title('TabPFN Feature Importance\n(Based on Permutation)', fontsize=14, weight='bold')
        ax1.grid(True, axis='x', alpha=0.3)

        # 子图2: SHAP重要性
        ax2.barh(y_pos, top_shap, color='lightcoral', alpha=0.8, edgecolor='black')
        ax2.set_yticks(y_pos)
        ax2.set_yticklabels(top_feature_names, fontsize=10)
        ax2.set_xlabel('Normalized Importance', fontsize=12)
        ax2.set_title('SHAP Feature Importance\n(Based on Attribution)', fontsize=14, weight='bold')
        ax2.grid(True, axis='x', alpha=0.3)

        # 子图3: 相关性散点图
        correlation = np.corrcoef(top_tabpfn, top_shap)[0, 1]
        ax3.scatter(top_tabpfn, top_shap, alpha=0.7, s=100, c='purple', edgecolors='black')

        # 添加特征名称标签
        for i, name in enumerate(top_feature_names):
            ax3.annotate(name, (top_tabpfn[i], top_shap[i]),
                        xytext=(5, 5), textcoords='offset points',
                        fontsize=8, alpha=0.7)

        # 添加对角线
        max_val = max(np.max(top_tabpfn), np.max(top_shap))
        ax3.plot([0, max_val], [0, max_val], 'r--', alpha=0.5, linewidth=2)

        ax3.set_xlabel('TabPFN Importance (Normalized)', fontsize=12)
        ax3.set_ylabel('SHAP Importance (Normalized)', fontsize=12)
        ax3.set_title(f'TabPFN vs SHAP Importance\nCorrelation: {correlation:.3f}', fontsize=14, weight='bold')
        ax3.grid(True, alpha=0.3)

        # 添加解释文本
        explanation_text = (
            "重要性差异原因:\n"
            "• TabPFN: 基于排列重要性，衡量特征对模型性能的影响\n"
            "• SHAP: 基于特征归因，衡量特征对预测结果的贡献\n"
            "• 两种方法从不同角度评估特征重要性，结果可能不同"
        )

        fig.text(0.02, 0.02, explanation_text, fontsize=10,
                bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8),
                verticalalignment='bottom')

        plt.tight_layout()
        plt.subplots_adjust(bottom=0.15)

        comparison_path_base = os.path.join(output_dir, f"{pinyin_species}_importance_comparison")
        _save_fig_to_formats(fig, comparison_path_base)
        print(f"重要性对比图已保存到: {comparison_path_base}.[png/pdf]")

    except Exception as e:
        print(f"  - 警告: 重要性对比分析失败: {str(e)}")
        plt.close()

    print("SHAP分析图表生成完毕（包括瀑布图、力图、总结图和重要性对比）。")

def plot_feature_correlation_heatmap(X_train_df: pd.DataFrame, output_dir: str, current_species: str):
    """绘制优化的特征相关性热力图，上三角显示相关系数，下三角显示显著性标记"""
    print("绘制优化的特征相关性热力图...")
    eng_species = SPECIES_MAP.get(current_species, current_species)
    pinyin_species = SPECIES_PINYIN_MAP.get(current_species, current_species)

    # 计算相关系数矩阵
    correlation_matrix = X_train_df.corr()
    n_features = len(correlation_matrix)

    # 计算显著性检验
    print("  - 计算相关系数显著性检验...")
    p_values = np.zeros((n_features, n_features))

    for i in range(n_features):
        for j in range(n_features):
            if i != j:
                # 计算Pearson相关系数的p值
                _, p_val = stats.pearsonr(X_train_df.iloc[:, i], X_train_df.iloc[:, j])
                p_values[i, j] = p_val
            else:
                p_values[i, j] = 0  # 对角线设为0

    # 创建显著性标记矩阵
    significance_matrix = np.full((n_features, n_features), '', dtype=object)
    for i in range(n_features):
        for j in range(n_features):
            if i != j:
                if p_values[i, j] < 0.001:
                    significance_matrix[i, j] = '***'
                elif p_values[i, j] < 0.01:
                    significance_matrix[i, j] = '**'
                elif p_values[i, j] < 0.05:
                    significance_matrix[i, j] = '*'
                else:
                    significance_matrix[i, j] = ''

    # 创建图形
    fig, ax = plt.subplots(figsize=(14, 12))

    # 创建掩码：上三角显示相关系数，下三角显示显著性
    mask_upper = np.triu(np.ones_like(correlation_matrix, dtype=bool), k=1)
    mask_lower = np.tril(np.ones_like(correlation_matrix, dtype=bool), k=-1)

    # 绘制上三角的相关系数热力图
    correlation_upper = correlation_matrix.copy()
    correlation_upper[mask_lower] = np.nan

    # 使用RdBu_r颜色映射，更好地区分正负相关
    sns.heatmap(correlation_upper,
                mask=mask_lower,
                cmap='RdBu_r',
                center=0,
                vmin=-1, vmax=1,
                annot=True,
                fmt='.2f',
                linewidths=0.5,
                linecolor='white',
                annot_kws={"size": 10, "weight": "bold"},
                cbar_kws={"shrink": 0.8, "label": "Correlation Coefficient"},
                ax=ax)

    # 在下三角添加显著性标记
    for i in range(n_features):
        for j in range(n_features):
            if i > j:  # 下三角
                # 根据显著性水平设置颜色
                if significance_matrix[i, j] == '***':
                    color = '#d62728'  # 红色
                    fontsize = 16
                elif significance_matrix[i, j] == '**':
                    color = '#ff7f0e'  # 橙色
                    fontsize = 14
                elif significance_matrix[i, j] == '*':
                    color = '#2ca02c'  # 绿色
                    fontsize = 12
                else:
                    color = '#7f7f7f'  # 灰色
                    fontsize = 10
                    significance_matrix[i, j] = 'n.s.'  # 不显著

                # 添加显著性标记
                ax.text(j + 0.5, i + 0.5, significance_matrix[i, j],
                       ha='center', va='center',
                       fontsize=fontsize, weight='bold', color=color)

    # 设置标题和标签
    ax.set_title(f'Feature Correlation Analysis for {eng_species}\n'
                f'Upper: Correlation Coefficients | Lower: Significance Levels',
                fontsize=16, weight='bold', pad=20)

    # 旋转标签以提高可读性
    ax.set_xticklabels(ax.get_xticklabels(), rotation=45, ha='right', fontsize=10)
    ax.set_yticklabels(ax.get_yticklabels(), rotation=0, fontsize=10)

    # 添加显著性图例
    legend_elements = [
        plt.Rectangle((0, 0), 1, 1, facecolor='#d62728', alpha=0.7, label='*** p < 0.001'),
        plt.Rectangle((0, 0), 1, 1, facecolor='#ff7f0e', alpha=0.7, label='** p < 0.01'),
        plt.Rectangle((0, 0), 1, 1, facecolor='#2ca02c', alpha=0.7, label='* p < 0.05'),
        plt.Rectangle((0, 0), 1, 1, facecolor='#7f7f7f', alpha=0.7, label='n.s. p ≥ 0.05')
    ]

    ax.legend(handles=legend_elements,
             loc='center left',
             bbox_to_anchor=(1.15, 0.5),
             title='Significance Levels',
             title_fontsize=12,
             fontsize=10)

    plt.tight_layout()

    output_path_base = os.path.join(output_dir, f"{pinyin_species}_feature_correlation_enhanced")
    _save_fig_to_formats(fig, output_path_base)

    print(f"优化的特征相关性热力图已保存到: {output_path_base}.[png/pdf]")

    # 输出显著相关性统计
    significant_pairs = []
    for i in range(n_features):
        for j in range(i+1, n_features):
            if p_values[i, j] < 0.05:
                significant_pairs.append({
                    'feature1': correlation_matrix.index[i],
                    'feature2': correlation_matrix.index[j],
                    'correlation': correlation_matrix.iloc[i, j],
                    'p_value': p_values[i, j],
                    'significance': significance_matrix[i, j] if significance_matrix[i, j] else '*'
                })

    if significant_pairs:
        print(f"  - 发现 {len(significant_pairs)} 对显著相关的特征:")
        for pair in sorted(significant_pairs, key=lambda x: abs(x['correlation']), reverse=True)[:10]:
            print(f"    {pair['feature1']} ↔ {pair['feature2']}: "
                  f"r={pair['correlation']:.3f}, p={pair['p_value']:.3e} {pair['significance']}")
    else:
        print("  - 未发现显著相关的特征对")

def calculate_bootstrap_ci_for_metrics(y_true: np.ndarray, y_pred_class: np.ndarray, y_pred_proba: np.ndarray, n_bootstraps: int = 1000, random_state: int = 42) -> Dict:
    """使用自助法为多个指标计算95%置信区间"""
    print(f"为最终测试集指标计算置信区间 (Bootstraps: {n_bootstraps})...")
    rng = np.random.RandomState(random_state)
    bootstrapped_scores = {'accuracy': [], 'f1': [], 'precision': [], 'recall': [], 'auc': []}
    
    for _ in range(n_bootstraps):
        try:
            indices = rng.randint(0, len(y_true), len(y_true))
            if len(np.unique(y_true[indices])) < 2: continue # 确保抽样后至少有两个类别
            
            bootstrapped_scores['accuracy'].append(accuracy_score(y_true[indices], y_pred_class[indices]))
            bootstrapped_scores['f1'].append(f1_score(y_true[indices], y_pred_class[indices], average='weighted'))
            bootstrapped_scores['precision'].append(precision_score(y_true[indices], y_pred_class[indices], average='weighted'))
            bootstrapped_scores['recall'].append(recall_score(y_true[indices], y_pred_class[indices], average='weighted'))
            bootstrapped_scores['auc'].append(roc_auc_score(y_true[indices], y_pred_proba[indices]))
        except ValueError:
            continue

    confidence_intervals = {}
    for metric, scores in bootstrapped_scores.items():
        if scores:
            lower = np.percentile(scores, 2.5)
            upper = np.percentile(scores, 97.5)
            confidence_intervals[metric] = (lower, upper)
    
    print("置信区间计算完成。")
    return confidence_intervals

# --- 鲁棒性验证模块 ---
class RobustnessValidator:
    """鲁棒性验证器，用于评估模型的稳定性和可靠性"""
    def __init__(self, noise_levels: List[float] = [0.01, 0.02, 0.05, 0.1, 0.2, 0.3, 0.5], n_bootstrap: int = 50, cv_folds: int = 5):
        self.noise_levels = noise_levels
        self.n_bootstrap = n_bootstrap
        self.cv_folds = cv_folds
        self.validation_results = {}

    def noise_robustness_test(self, model, X_val_processed: np.ndarray, y_val: np.ndarray, n_repeats: int = 20):
        """
        增强版噪声鲁棒性测试。
        在经过预处理的验证集上注入不同级别的噪声，并评估预训练模型的性能变化。
        为每个噪声级别运行多次以获得更稳定的结果。
        """
        print("  - 执行增强版噪声鲁棒性测试...")
        results = {
            'noise_levels': [0.0] + self.noise_levels,
            'mean_accuracy': [], 'std_accuracy': [],
            'mean_f1': [], 'std_f1': [],
            'mean_precision': [], 'std_precision': [],
            'mean_recall': [], 'std_recall': [],
            'mean_auc': [], 'std_auc': []
        }

        # 步骤1: 获取基线性能（在无噪声的验证集上）
        y_pred_baseline = model.predict(X_val_processed)
        y_pred_proba_baseline = model.predict_proba(X_val_processed)[:, 1]

        results['mean_accuracy'].append(accuracy_score(y_val, y_pred_baseline))
        results['std_accuracy'].append(0.0)
        results['mean_f1'].append(f1_score(y_val, y_pred_baseline, average='weighted'))
        results['std_f1'].append(0.0)
        results['mean_precision'].append(precision_score(y_val, y_pred_baseline, average='weighted'))
        results['std_precision'].append(0.0)
        results['mean_recall'].append(recall_score(y_val, y_pred_baseline, average='weighted'))
        results['std_recall'].append(0.0)
        results['mean_auc'].append(roc_auc_score(y_val, y_pred_proba_baseline))
        results['std_auc'].append(0.0)

        # 步骤2: 注入噪声并重复评估
        for noise_level in self.noise_levels:
            accuracies, f1_scores, precisions, recalls, aucs = [], [], [], [], []

            for repeat in range(n_repeats):
                # 设置随机种子确保可重现性
                np.random.seed(42 + repeat)

                # 相对于特征的标准差添加噪声
                noise = np.random.normal(0, noise_level * np.std(X_val_processed, axis=0), X_val_processed.shape)
                X_val_noisy = X_val_processed + noise

                # 使用现有模型进行预测
                y_pred_noisy = model.predict(X_val_noisy)
                y_pred_proba_noisy = model.predict_proba(X_val_noisy)[:, 1]

                # 记录多个指标
                accuracies.append(accuracy_score(y_val, y_pred_noisy))
                f1_scores.append(f1_score(y_val, y_pred_noisy, average='weighted'))
                precisions.append(precision_score(y_val, y_pred_noisy, average='weighted'))
                recalls.append(recall_score(y_val, y_pred_noisy, average='weighted'))
                aucs.append(roc_auc_score(y_val, y_pred_proba_noisy))

            # 记录多次运行的均值和标准差
            results['mean_accuracy'].append(np.mean(accuracies))
            results['std_accuracy'].append(np.std(accuracies))
            results['mean_f1'].append(np.mean(f1_scores))
            results['std_f1'].append(np.std(f1_scores))
            results['mean_precision'].append(np.mean(precisions))
            results['std_precision'].append(np.std(precisions))
            results['mean_recall'].append(np.mean(recalls))
            results['std_recall'].append(np.std(recalls))
            results['mean_auc'].append(np.mean(aucs))
            results['std_auc'].append(np.std(aucs))

        print(f"  - 噪声测试完成。基线准确率: {results['mean_accuracy'][0]:.3f}, "
              f"最高噪声下平均准确率: {results['mean_accuracy'][-1]:.3f} ± {results['std_accuracy'][-1]:.3f}")
        return results

    def feature_dropout_test(self, model, X_val_processed: np.ndarray, y_val: np.ndarray, dropout_rates=None):
        """
        特征丢弃测试：随机丢弃一定比例的特征，评估模型性能
        """
        print("  - 执行特征丢弃鲁棒性测试...")
        if dropout_rates is None:
            dropout_rates = [0.1, 0.2, 0.3, 0.4, 0.5]

        results = {
            'dropout_rates': [0.0] + dropout_rates,
            'mean_accuracy': [], 'std_accuracy': [],
            'mean_f1': [], 'std_f1': []
        }

        # 基线性能
        y_pred_baseline = model.predict(X_val_processed)
        results['mean_accuracy'].append(accuracy_score(y_val, y_pred_baseline))
        results['std_accuracy'].append(0.0)
        results['mean_f1'].append(f1_score(y_val, y_pred_baseline, average='weighted'))
        results['std_f1'].append(0.0)

        n_features = X_val_processed.shape[1]
        n_repeats = 15

        for dropout_rate in dropout_rates:
            accuracies, f1_scores = [], []

            for repeat in range(n_repeats):
                np.random.seed(42 + repeat)

                # 随机选择要丢弃的特征
                n_drop = int(n_features * dropout_rate)
                drop_indices = np.random.choice(n_features, n_drop, replace=False)

                # 创建丢弃特征后的数据
                X_val_dropped = X_val_processed.copy()
                X_val_dropped[:, drop_indices] = 0  # 将选中的特征设为0

                # 预测
                y_pred_dropped = model.predict(X_val_dropped)

                accuracies.append(accuracy_score(y_val, y_pred_dropped))
                f1_scores.append(f1_score(y_val, y_pred_dropped, average='weighted'))

            results['mean_accuracy'].append(np.mean(accuracies))
            results['std_accuracy'].append(np.std(accuracies))
            results['mean_f1'].append(np.mean(f1_scores))
            results['std_f1'].append(np.std(f1_scores))

        print(f"  - 特征丢弃测试完成。基线准确率: {results['mean_accuracy'][0]:.3f}")
        return results

    def sample_perturbation_test(self, model, X_val_processed: np.ndarray, y_val: np.ndarray, perturbation_strengths=None):
        """
        样本扰动测试：对每个样本添加不同强度的随机扰动
        """
        print("  - 执行样本扰动鲁棒性测试...")
        if perturbation_strengths is None:
            perturbation_strengths = [0.01, 0.05, 0.1, 0.2, 0.3]

        results = {
            'perturbation_strengths': [0.0] + perturbation_strengths,
            'mean_accuracy': [], 'std_accuracy': [],
            'mean_f1': [], 'std_f1': []
        }

        # 基线性能
        y_pred_baseline = model.predict(X_val_processed)
        results['mean_accuracy'].append(accuracy_score(y_val, y_pred_baseline))
        results['std_accuracy'].append(0.0)
        results['mean_f1'].append(f1_score(y_val, y_pred_baseline, average='weighted'))
        results['std_f1'].append(0.0)

        n_repeats = 15

        for strength in perturbation_strengths:
            accuracies, f1_scores = [], []

            for repeat in range(n_repeats):
                np.random.seed(42 + repeat)

                # 为每个样本添加独立的随机扰动
                perturbation = np.random.uniform(-strength, strength, X_val_processed.shape)
                X_val_perturbed = X_val_processed + perturbation

                # 预测
                y_pred_perturbed = model.predict(X_val_perturbed)

                accuracies.append(accuracy_score(y_val, y_pred_perturbed))
                f1_scores.append(f1_score(y_val, y_pred_perturbed, average='weighted'))

            results['mean_accuracy'].append(np.mean(accuracies))
            results['std_accuracy'].append(np.std(accuracies))
            results['mean_f1'].append(np.mean(f1_scores))
            results['std_f1'].append(np.std(f1_scores))

        print(f"  - 样本扰动测试完成。基线准确率: {results['mean_accuracy'][0]:.3f}")
        return results

    def _plot_noise_robustness(self, results: Dict, output_dir: str, filename_prefix: str):
        """绘制增强版噪声鲁棒性测试结果"""
        print("  - 绘制增强版噪声鲁棒性图...")
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 子图1: 准确率和F1分数
        ax1 = axes[0, 0]
        ax1.plot(results['noise_levels'], results['mean_accuracy'], 'o-', label='Accuracy', linewidth=2)
        ax1.fill_between(results['noise_levels'],
                         np.array(results['mean_accuracy']) - np.array(results['std_accuracy']),
                         np.array(results['mean_accuracy']) + np.array(results['std_accuracy']),
                         alpha=0.3)

        ax1.plot(results['noise_levels'], results['mean_f1'], 's--', label='F1 Score', linewidth=2)
        ax1.fill_between(results['noise_levels'],
                         np.array(results['mean_f1']) - np.array(results['std_f1']),
                         np.array(results['mean_f1']) + np.array(results['std_f1']),
                         alpha=0.3)

        ax1.set_xlabel("Noise Level (std dev fraction)", fontsize=12)
        ax1.set_ylabel("Score", fontsize=12)
        ax1.set_title("Accuracy & F1 Score vs Noise", fontsize=14, weight='bold')
        ax1.legend(fontsize=10)
        ax1.grid(True, alpha=0.3)

        # 子图2: 精确率和召回率
        ax2 = axes[0, 1]
        if 'mean_precision' in results:
            ax2.plot(results['noise_levels'], results['mean_precision'], '^-', label='Precision', linewidth=2)
            ax2.fill_between(results['noise_levels'],
                             np.array(results['mean_precision']) - np.array(results['std_precision']),
                             np.array(results['mean_precision']) + np.array(results['std_precision']),
                             alpha=0.3)

            ax2.plot(results['noise_levels'], results['mean_recall'], 'v--', label='Recall', linewidth=2)
            ax2.fill_between(results['noise_levels'],
                             np.array(results['mean_recall']) - np.array(results['std_recall']),
                             np.array(results['mean_recall']) + np.array(results['std_recall']),
                             alpha=0.3)

        ax2.set_xlabel("Noise Level (std dev fraction)", fontsize=12)
        ax2.set_ylabel("Score", fontsize=12)
        ax2.set_title("Precision & Recall vs Noise", fontsize=14, weight='bold')
        ax2.legend(fontsize=10)
        ax2.grid(True, alpha=0.3)

        # 子图3: AUC
        ax3 = axes[1, 0]
        if 'mean_auc' in results:
            ax3.plot(results['noise_levels'], results['mean_auc'], 'd-', label='AUC', linewidth=2, color='red')
            ax3.fill_between(results['noise_levels'],
                             np.array(results['mean_auc']) - np.array(results['std_auc']),
                             np.array(results['mean_auc']) + np.array(results['std_auc']),
                             alpha=0.3, color='red')

        ax3.set_xlabel("Noise Level (std dev fraction)", fontsize=12)
        ax3.set_ylabel("AUC Score", fontsize=12)
        ax3.set_title("AUC vs Noise", fontsize=14, weight='bold')
        ax3.legend(fontsize=10)
        ax3.grid(True, alpha=0.3)

        # 子图4: 性能下降百分比
        ax4 = axes[1, 1]
        baseline_acc = results['mean_accuracy'][0]
        acc_degradation = [(baseline_acc - acc) / baseline_acc * 100 for acc in results['mean_accuracy']]
        ax4.plot(results['noise_levels'], acc_degradation, 'o-', label='Accuracy Degradation (%)', linewidth=2, color='orange')

        if 'mean_auc' in results:
            baseline_auc = results['mean_auc'][0]
            auc_degradation = [(baseline_auc - auc) / baseline_auc * 100 for auc in results['mean_auc']]
            ax4.plot(results['noise_levels'], auc_degradation, 's--', label='AUC Degradation (%)', linewidth=2, color='purple')

        ax4.set_xlabel("Noise Level (std dev fraction)", fontsize=12)
        ax4.set_ylabel("Performance Degradation (%)", fontsize=12)
        ax4.set_title("Performance Degradation vs Noise", fontsize=14, weight='bold')
        ax4.legend(fontsize=10)
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()

        output_path_base = os.path.join(output_dir, f"{filename_prefix}_enhanced_noise_robustness")
        _save_fig_to_formats(fig, output_path_base)
        print(f"  - 增强版噪声鲁棒性图已保存到: {output_path_base}.[png/pdf]")

    def _plot_feature_dropout_robustness(self, results: Dict, output_dir: str, filename_prefix: str):
        """绘制特征丢弃鲁棒性测试结果"""
        print("  - 绘制特征丢弃鲁棒性图...")
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # 子图1: 性能vs丢弃率
        ax1.plot(results['dropout_rates'], results['mean_accuracy'], 'o-', label='Accuracy', linewidth=2)
        ax1.fill_between(results['dropout_rates'],
                         np.array(results['mean_accuracy']) - np.array(results['std_accuracy']),
                         np.array(results['mean_accuracy']) + np.array(results['std_accuracy']),
                         alpha=0.3)

        ax1.plot(results['dropout_rates'], results['mean_f1'], 's--', label='F1 Score', linewidth=2)
        ax1.fill_between(results['dropout_rates'],
                         np.array(results['mean_f1']) - np.array(results['std_f1']),
                         np.array(results['mean_f1']) + np.array(results['std_f1']),
                         alpha=0.3)

        ax1.set_xlabel("Feature Dropout Rate", fontsize=12)
        ax1.set_ylabel("Score", fontsize=12)
        ax1.set_title("Performance vs Feature Dropout Rate", fontsize=14, weight='bold')
        ax1.legend(fontsize=10)
        ax1.grid(True, alpha=0.3)

        # 子图2: 性能下降百分比
        baseline_acc = results['mean_accuracy'][0]
        acc_degradation = [(baseline_acc - acc) / baseline_acc * 100 for acc in results['mean_accuracy']]
        ax2.plot(results['dropout_rates'], acc_degradation, 'o-', label='Accuracy Degradation (%)', linewidth=2, color='red')

        baseline_f1 = results['mean_f1'][0]
        f1_degradation = [(baseline_f1 - f1) / baseline_f1 * 100 for f1 in results['mean_f1']]
        ax2.plot(results['dropout_rates'], f1_degradation, 's--', label='F1 Degradation (%)', linewidth=2, color='orange')

        ax2.set_xlabel("Feature Dropout Rate", fontsize=12)
        ax2.set_ylabel("Performance Degradation (%)", fontsize=12)
        ax2.set_title("Performance Degradation vs Dropout Rate", fontsize=14, weight='bold')
        ax2.legend(fontsize=10)
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()

        output_path_base = os.path.join(output_dir, f"{filename_prefix}_feature_dropout_robustness")
        _save_fig_to_formats(fig, output_path_base)
        print(f"  - 特征丢弃鲁棒性图已保存到: {output_path_base}.[png/pdf]")

    def _plot_sample_perturbation_robustness(self, results: Dict, output_dir: str, filename_prefix: str):
        """绘制样本扰动鲁棒性测试结果"""
        print("  - 绘制样本扰动鲁棒性图...")
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # 子图1: 性能vs扰动强度
        ax1.plot(results['perturbation_strengths'], results['mean_accuracy'], 'o-', label='Accuracy', linewidth=2)
        ax1.fill_between(results['perturbation_strengths'],
                         np.array(results['mean_accuracy']) - np.array(results['std_accuracy']),
                         np.array(results['mean_accuracy']) + np.array(results['std_accuracy']),
                         alpha=0.3)

        ax1.plot(results['perturbation_strengths'], results['mean_f1'], 's--', label='F1 Score', linewidth=2)
        ax1.fill_between(results['perturbation_strengths'],
                         np.array(results['mean_f1']) - np.array(results['std_f1']),
                         np.array(results['mean_f1']) + np.array(results['std_f1']),
                         alpha=0.3)

        ax1.set_xlabel("Perturbation Strength", fontsize=12)
        ax1.set_ylabel("Score", fontsize=12)
        ax1.set_title("Performance vs Sample Perturbation", fontsize=14, weight='bold')
        ax1.legend(fontsize=10)
        ax1.grid(True, alpha=0.3)

        # 子图2: 性能下降百分比
        baseline_acc = results['mean_accuracy'][0]
        acc_degradation = [(baseline_acc - acc) / baseline_acc * 100 for acc in results['mean_accuracy']]
        ax2.plot(results['perturbation_strengths'], acc_degradation, 'o-', label='Accuracy Degradation (%)', linewidth=2, color='red')

        baseline_f1 = results['mean_f1'][0]
        f1_degradation = [(baseline_f1 - f1) / baseline_f1 * 100 for f1 in results['mean_f1']]
        ax2.plot(results['perturbation_strengths'], f1_degradation, 's--', label='F1 Degradation (%)', linewidth=2, color='orange')

        ax2.set_xlabel("Perturbation Strength", fontsize=12)
        ax2.set_ylabel("Performance Degradation (%)", fontsize=12)
        ax2.set_title("Performance Degradation vs Perturbation", fontsize=14, weight='bold')
        ax2.legend(fontsize=10)
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()

        output_path_base = os.path.join(output_dir, f"{filename_prefix}_sample_perturbation_robustness")
        _save_fig_to_formats(fig, output_path_base)
        print(f"  - 样本扰动鲁棒性图已保存到: {output_path_base}.[png/pdf]")

    def comprehensive_robustness_evaluation(self, model, X_train_final, y_train, X_val_final, y_val, output_dir, species):
        """执行并保存所有鲁棒性评估"""
        print("\n--- 开始综合鲁棒性评估 ---")

        # 1. 增强版噪声鲁棒性测试
        print("1. 执行增强版噪声鲁棒性测试...")
        noise_results = self.noise_robustness_test(model, X_val_final, y_val)
        self._plot_noise_robustness(noise_results, output_dir, species)
        self.validation_results['noise_robustness'] = noise_results

        # 2. 特征丢弃鲁棒性测试
        print("2. 执行特征丢弃鲁棒性测试...")
        dropout_results = self.feature_dropout_test(model, X_val_final, y_val)
        self._plot_feature_dropout_robustness(dropout_results, output_dir, species)
        self.validation_results['feature_dropout'] = dropout_results

        # 3. 样本扰动鲁棒性测试
        print("3. 执行样本扰动鲁棒性测试...")
        perturbation_results = self.sample_perturbation_test(model, X_val_final, y_val)
        self._plot_sample_perturbation_robustness(perturbation_results, output_dir, species)
        self.validation_results['sample_perturbation'] = perturbation_results

        # 4. 生成综合鲁棒性报告
        print("4. 生成综合鲁棒性报告...")
        self._generate_robustness_report(output_dir, species)

        # 保存JSON结果
        output_path = ensure_dir_exists(os.path.join(output_dir, f"{species}_robustness_report.json"))
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.validation_results, f, indent=2, ensure_ascii=False)
        print(f"综合鲁棒性JSON报告已保存到: {output_path}")

        print("--- 综合鲁棒性评估完成 ---\n")

    def _generate_robustness_report(self, output_dir: str, species: str):
        """生成综合鲁棒性评估报告"""
        report_path = os.path.join(output_dir, f"{species}_robustness_report.txt")

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(f"=== {species} 模型鲁棒性评估报告 ===\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # 噪声鲁棒性结果
            if 'noise_robustness' in self.validation_results:
                results = self.validation_results['noise_robustness']
                f.write("1. 噪声鲁棒性测试结果:\n")
                f.write(f"   基线准确率: {results['mean_accuracy'][0]:.3f}\n")
                f.write(f"   最高噪声水平: {results['noise_levels'][-1]}\n")
                f.write(f"   最高噪声下准确率: {results['mean_accuracy'][-1]:.3f} ± {results['std_accuracy'][-1]:.3f}\n")
                f.write(f"   性能下降: {(results['mean_accuracy'][0] - results['mean_accuracy'][-1]) / results['mean_accuracy'][0] * 100:.1f}%\n\n")

            # 特征丢弃鲁棒性结果
            if 'feature_dropout' in self.validation_results:
                results = self.validation_results['feature_dropout']
                f.write("2. 特征丢弃鲁棒性测试结果:\n")
                f.write(f"   基线准确率: {results['mean_accuracy'][0]:.3f}\n")
                f.write(f"   最高丢弃率: {results['dropout_rates'][-1]}\n")
                f.write(f"   最高丢弃率下准确率: {results['mean_accuracy'][-1]:.3f} ± {results['std_accuracy'][-1]:.3f}\n")
                f.write(f"   性能下降: {(results['mean_accuracy'][0] - results['mean_accuracy'][-1]) / results['mean_accuracy'][0] * 100:.1f}%\n\n")

            # 样本扰动鲁棒性结果
            if 'sample_perturbation' in self.validation_results:
                results = self.validation_results['sample_perturbation']
                f.write("3. 样本扰动鲁棒性测试结果:\n")
                f.write(f"   基线准确率: {results['mean_accuracy'][0]:.3f}\n")
                f.write(f"   最高扰动强度: {results['perturbation_strengths'][-1]}\n")
                f.write(f"   最高扰动下准确率: {results['mean_accuracy'][-1]:.3f} ± {results['std_accuracy'][-1]:.3f}\n")
                f.write(f"   性能下降: {(results['mean_accuracy'][0] - results['mean_accuracy'][-1]) / results['mean_accuracy'][0] * 100:.1f}%\n\n")

            f.write("4. 鲁棒性评估结论:\n")
            f.write("   - 模型在多种扰动条件下的稳定性已通过测试\n")
            f.write("   - 详细的性能变化趋势已记录在相应的可视化图表中\n")
            f.write("   - 建议在实际应用中考虑数据质量对模型性能的影响\n")

        print(f"  - 鲁棒性报告已保存到: {report_path}")


# --- 主训练与验证流程 ---
def train_and_validate(train_data_path: str, val_data_path: str, test_data_path: str, features_path: str, output_dir: str, fish_type: str, random_state: int):
    """主训练和验证函数"""
    print(f"======== 开始处理: {fish_type} ========")
    device = check_cuda_availability()
    print(f"  - 设备类型: {device}")

    # 1. 创建输出目录
    visualization_dir = ensure_dir_exists(os.path.join(output_dir, 'visualization', fish_type, ''))
    model_dir = ensure_dir_exists(os.path.join(output_dir, 'models', fish_type, ''))
    
    # 2. 加载数据和特征
    print(f"\n[1/7] 加载已划分的数据集和最终特征列表")
    print(f"  - 训练集: {train_data_path}")
    print(f"  - 验证集: {val_data_path}")
    print(f"  - 测试集: {test_data_path}")
    print(f"  - 特征集: {features_path}")
    
    train_data = pd.read_csv(train_data_path)
    val_data = pd.read_csv(val_data_path)
    test_data = pd.read_csv(test_data_path)
    final_features_df = pd.read_csv(features_path)
    features = final_features_df['feature'].tolist()

    # 3. 预处理与特征筛选
    print(f"\n[2/7] 预处理标签并根据特征列表筛选数据")
    # 统一处理所有数据集的样本ID列
    for df in [train_data, val_data, test_data]:
        # [修改] 使用 'Group' 列作为标签
        if 'Group' not in df.columns:
            raise ValueError("输入数据中未找到 'Group' 列")
        
        # 确保所有需要的特征都存在
        missing_features = [f for f in features if f not in df.columns]
        if missing_features:
            raise ValueError(f"数据中缺少以下最终特征: {', '.join(missing_features)}")
            
        # [修改] 移除 SampleID 列
        sample_id_col = 'SampleID'
        if sample_id_col in df.columns:
            df.drop(sample_id_col, axis=1, inplace=True)

    # 根据最终特征列表筛选数据
    X_train_raw = train_data[features]
    y_train_raw = train_data['Group']
    
    X_val_raw = val_data[features]
    y_val_raw = val_data['Group']
    
    X_test_raw = test_data[features]
    y_test_raw = test_data['Group']
    
    le = LabelEncoder()
    y_train = le.fit_transform(y_train_raw)
    y_val = le.transform(y_val_raw)
    y_test = le.transform(y_test_raw)
    
    class_names = le.classes_
    eng_class_names = [CLASS_LABEL_MAP.get(name, name) for name in class_names]
    
    # --- [修改] 明确说明跳过内部数据划分和转换 ---
    print("\n[3/7] 数据集已由用户预先划分，跳过内部划分步骤。")
    print(f"  - 训练集大小: {len(X_train_raw)}")
    print(f"  - 验证集大小: {len(X_val_raw)}")
    print(f"  - 测试集大小: {len(X_test_raw)}")

    print("\n[4/7] 使用用户提供的原始丰度数据，跳过内部数据转换步骤。")
    X_train_final = X_train_raw.values
    X_val_final = X_val_raw.values
    X_test_final = X_test_raw.values
    
    # 5. 模型训练
    print("\n[5/7] 模型训练")
    tabpfn_classifier = TabPFNClassifier(device=device)
    tabpfn_classifier.fit(X_train_final, y_train)
    models = {'TabPFN': tabpfn_classifier}
    
    # 6. 模型评估与可视化
    print("\n[6/7] 性能评估与可视化")
    y_train_pred = tabpfn_classifier.predict(X_train_final)
    y_val_pred = tabpfn_classifier.predict(X_val_final)
    
    train_metrics = {'TabPFN': {'accuracy': accuracy_score(y_train, y_train_pred), 'f1': f1_score(y_train, y_train_pred, average='weighted')}}
    val_metrics = {'TabPFN': {'accuracy': accuracy_score(y_val, y_val_pred), 'f1': f1_score(y_val, y_val_pred, average='weighted')}}
    
    _, optimal_thresholds = plot_roc_curves(models, X_val_final, y_val, visualization_dir, fish_type)
    
    # 在测试集上应用最佳阈值进行预测 (或保持0.5，取决于策略)
    y_test_pred_optim = (tabpfn_classifier.predict_proba(X_test_final)[:, 1] >= optimal_thresholds['TabPFN']).astype(int)
    y_test_pred = tabpfn_classifier.predict(X_test_final)

    plot_all_confusion_matrices(
        y_train, y_train_pred, y_val, y_val_pred, y_test, y_test_pred,
        eng_class_names, visualization_dir, "TabPFN", fish_type
    )

    plot_tabpfn_feature_importance(tabpfn_classifier, X_val_final, y_val, features, visualization_dir, fish_type)
    plot_learning_curves(tabpfn_classifier, X_train_final, y_train, visualization_dir, fish_type)
    
    X_train_final_df = pd.DataFrame(X_train_final, columns=features)
    X_val_final_df = pd.DataFrame(X_val_final, columns=features)
    
    plot_feature_correlation_heatmap(X_train_final_df, visualization_dir, fish_type)
    plot_tabpfn_shap_analysis(tabpfn_classifier, X_train_final_df, X_val_final_df, y_val, features, eng_class_names, visualization_dir, fish_type)
    
    # 7. 最终测试集评估与报告
    print("\n[7/7] 在独立的最终测试集上进行最终评估...")
    y_test_pred_proba = tabpfn_classifier.predict_proba(X_test_final)[:, 1]
    
    test_metrics = {
        'accuracy': accuracy_score(y_test, y_test_pred),
        'f1': f1_score(y_test, y_test_pred, average='weighted'),
        'precision': precision_score(y_test, y_test_pred, average='weighted'),
        'recall': recall_score(y_test, y_test_pred, average='weighted'),
        'auc': roc_auc_score(y_test, y_test_pred_proba)
    }
    
    # --- [新增] 计算置信区间 ---
    test_metrics_ci = calculate_bootstrap_ci_for_metrics(y_test, y_test_pred, y_test_pred_proba, random_state=random_state)
    
    # 将CI合并到主指标字典中
    for metric, (lower, upper) in test_metrics_ci.items():
        test_metrics[f'{metric}_ci'] = (lower, upper)
        
    print(f"  - 最终测试集性能 (含95% CI):")
    for metric, value in test_metrics.items():
        if '_ci' not in metric:
            ci_str = f" (95% CI: {test_metrics.get(f'{metric}_ci', (np.nan, np.nan))[0]:.4f}-{test_metrics.get(f'{metric}_ci', (np.nan, np.nan))[1]:.4f})"
            print(f"    - {metric.capitalize()}: {value:.4f}{ci_str}")

    save_metrics_to_text(val_metrics, visualization_dir, fish_type, train_metrics, optimal_thresholds, test_metrics)
    
    # 鲁棒性验证
    print("\n[Bonus] 执行鲁棒性诊断")
    validator = RobustnessValidator()
    validator.comprehensive_robustness_evaluation(tabpfn_classifier, X_train_final, y_train, X_val_final, y_val, visualization_dir, fish_type)

    # 保存模型及相关文件
    pinyin_fish_type = SPECIES_PINYIN_MAP.get(fish_type, fish_type)
    dump(tabpfn_classifier, os.path.join(model_dir, f'{pinyin_fish_type}_model.joblib'))
    dump(le, os.path.join(model_dir, f'{pinyin_fish_type}_encoder.joblib'))
    with open(os.path.join(model_dir, 'features.json'), 'w') as f: json.dump(features, f)

    print("\n" + "="*60 + f"\n为'{fish_type}'的简化版训练流程执行完毕!\n" + "="*60)


if __name__ == "__main__":
    # ==============================================================================
    # --- 用户配置区 ---
    # ==============================================================================
    
    # 1. 定义物种类型 ('鳙鱼' 或 '鲢鱼')
    #    这会影响输出文件的命名
    FISH_TYPE = '鳙鱼'

    # 2. 定义文件路径 (请确保使用正斜杠 / 作为路径分隔符)
    #    主输出目录
    BASE_OUTPUT_DIR = "C:/Users/<USER>/Desktop/"
    
    #    R脚本输出的目录
    R_OUTPUT_DIR = os.path.join(BASE_OUTPUT_DIR, f"{FISH_TYPE}Boruta_Pipeline_V2_Output")
    
    #    Python脚本的输出目录
    PYTHON_OUTPUT_DIR = os.path.join(BASE_OUTPUT_DIR, "Python_Pipeline_Output")

    # 3. 自动构建输入文件路径
    TRAIN_DATA_PATH = os.path.join(R_OUTPUT_DIR, "2_Elite_Features_Data", "elite_features_TRAIN_raw.csv")
    VAL_DATA_PATH = os.path.join(R_OUTPUT_DIR, "2_Elite_Features_Data", "elite_features_VALIDATION_raw.csv")
    TEST_DATA_PATH = os.path.join(R_OUTPUT_DIR, "2_Elite_Features_Data", "elite_features_TEST_raw.csv")
    FEATURES_FILE_PATH = os.path.join(PYTHON_OUTPUT_DIR, "final_optimized_feature_set.csv")
    
    # 4. 其他参数
    RANDOM_STATE = 42

    # ==============================================================================
    # --- 脚本主逻辑 ---
    # ==============================================================================
    try:
        # 确保Python输出目录存在
        ensure_dir_exists(os.path.join(PYTHON_OUTPUT_DIR, 'dummy.txt'))

        train_and_validate(
            train_data_path=TRAIN_DATA_PATH,
            val_data_path=VAL_DATA_PATH,
            test_data_path=TEST_DATA_PATH,
            features_path=FEATURES_FILE_PATH,
            output_dir=PYTHON_OUTPUT_DIR, # 主输出目录改为Python专用的
            fish_type=FISH_TYPE,
            random_state=RANDOM_STATE
        )
    except Exception as e:
        print(f"\nFATAL ERROR: 程序执行失败: {str(e)}")
        traceback.print_exc()
        sys.exit(1) 