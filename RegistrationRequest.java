# 创建新的 RegistrationRequest.java 文件，包含 code 字段和验证
cat > /var/www/api/fish-api/src/main/java/com/example/fishapi/dto/RegistrationRequest.java << EOF
package com.example.fishapi.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

public class RegistrationRequest {

    @NotBlank(message = "Username cannot be blank")
    private String username;

    @NotBlank(message = "Email cannot be blank")
    @Email(message = "Invalid email format")
    private String email;

    @NotBlank(message = "Password cannot be blank")
    @Size(min = 6, message = "Password must be at least 6 characters long")
    private String password;

    @NotBlank(message = "Verification code cannot be blank") // *** 新增 code 字段 ***
    private String code;

    // Getters and Setters
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }
    public String getCode() { return code; } // *** 新增 Getter/Setter ***
    public void setCode(String code) { this.code = code; }
}
EOF