/var/www/api/fish-api/src/main/java/com/example/fishapi/repository/PasswordResetTokenRepository.java
package com.example.fishapi.repository;

import com.example.fishapi.model.PasswordResetToken;
import com.example.fishapi.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface PasswordResetTokenRepository extends JpaRepository<PasswordResetToken, Long> {

    Optional<PasswordResetToken> findByToken(String token);

    Optional<PasswordResetToken> findByUser(User user);

    void deleteByUser(User user); // 删除用户的所有旧token

    void deleteByExpiryDateLessThan(java.util.Date now); // 用于定时清理过期token
}