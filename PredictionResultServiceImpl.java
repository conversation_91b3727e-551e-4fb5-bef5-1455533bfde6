//sudo bash -c 'cat << EOF > /var/www/api/fish-api/src/main/java/com/example/fishapi/service/impl/PredictionResultServiceImpl.java
package com.example.fishapi.service.impl;

import com.example.fishapi.event.TaskStatusChangeEvent;
import org.springframework.context.ApplicationEventPublisher;
import com.example.fishapi.dto.PredictionResultDto;
import com.example.fishapi.dto.PredictionResultDetailDto;
import com.example.fishapi.dto.PredictionSummaryDTO;
import com.example.fishapi.model.FishSpecies;
import com.example.fishapi.model.PredictionResult;
import com.example.fishapi.model.PredictionResultDetail;
import com.example.fishapi.model.PredictionSummary;
import com.example.fishapi.model.PredictionTask;
import com.example.fishapi.model.User;
import com.example.fishapi.model.PredictionTask.TaskStatus;
import com.example.fishapi.repository.FishSpeciesRepository;
import com.example.fishapi.repository.PredictionResultRepository;
import com.example.fishapi.repository.PredictionTaskRepository;
import com.example.fishapi.repository.UserRepository;
import com.example.fishapi.service.PredictionResultService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityNotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PredictionResultServiceImpl implements PredictionResultService {

    @Autowired
    private PredictionResultRepository predictionResultRepository;

    @Autowired
    private PredictionTaskRepository predictionTaskRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private FishSpeciesRepository fishSpeciesRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Override
    @Transactional
    public PredictionResultDto createOrUpdatePredictionResult(Long taskId, String rawModelResponse, String username) {
         User user = userRepository.findByUsername(username)
                 .orElseThrow(() -> new EntityNotFoundException("未找到用户: " + username));
         PredictionTask task = predictionTaskRepository.findById(taskId)
                 .orElseThrow(() -> new EntityNotFoundException("未找到预测任务: " + taskId));

        if (!StringUtils.hasText(rawModelResponse)) {
             log.warn("任务 {} 的模型响应为空，无法创建结果。将任务标记为 FAILED。", taskId);
             task.setStatus(TaskStatus.FAILED);
             predictionTaskRepository.save(task);
             return null;
        }

        log.info("正在为任务 {} 创建/更新预测结果。", taskId);
        PredictionResult result = predictionResultRepository.findByPredictionTask_Id(taskId)
                .orElse(new PredictionResult());
        result.setPredictionTask(task);
        result.setRawModelResponse(rawModelResponse);

        result.getResultDetails().clear();
        if (result.getPredictionSummary() != null) {
             result.getPredictionSummary().setPredictionResult(null);
             result.setPredictionSummary(null);
        }

        boolean parseSuccess = false;
        try {
            parseSuccess = parseAndPopulateResult(result, rawModelResponse);
             if (!parseSuccess) {
                  log.warn("任务 {} 的响应解析或处理未成功完成。将标记为 FAILED。", taskId);
                  if (result.getPredictionSummary() == null) {
                      result.setPredictionSummary(createErrorSummary("解析或处理失败"));
                  }
                  if (result.getPredictionSummary() != null) {
                      result.getPredictionSummary().setPredictionResult(result);
                      result.getPredictionSummary().setPredictionTask(task);
                  }
                  PredictionResult savedResultWithError = predictionResultRepository.save(result);
                  task.setStatus(TaskStatus.FAILED);
                  predictionTaskRepository.save(task);
                  return convertToDto(savedResultWithError);
             }
        } catch (Exception e) {
             log.error("处理任务 {} 的模型响应时发生意外错误: {}", taskId, e.getMessage(), e);
             try {
                 result.setPredictionSummary(createErrorSummary("处理错误: " + e.getMessage()));
                 result.getPredictionSummary().setPredictionResult(result);
                 result.getPredictionSummary().setPredictionTask(task);
                 PredictionResult savedResultWithError = predictionResultRepository.save(result);
                 task.setStatus(TaskStatus.FAILED);
                 predictionTaskRepository.save(task);
                 return convertToDto(savedResultWithError);
             } catch (Exception saveEx) {
                  log.error("保存任务 {} 的预测结果（处理错误后）时出错: {}", taskId, saveEx.getMessage(), saveEx);
                  task.setStatus(TaskStatus.FAILED);
                  predictionTaskRepository.save(task);
                  throw new RuntimeException("保存预测结果失败（在处理错误后）", saveEx);
             }
        }

        try {
            if (result.getPredictionSummary() != null) {
                if (result.getPredictionSummary().getPredictionResult() == null) {
                    result.getPredictionSummary().setPredictionResult(result);
                }
                if (result.getPredictionSummary().getPredictionTask() == null) {
                     result.getPredictionSummary().setPredictionTask(task);
                }
            }
            PredictionResult savedResult = predictionResultRepository.save(result);
            log.info("预测结果已成功解析并保存: ID={}, 任务ID={}", savedResult.getId(), taskId);
            task.setStatus(TaskStatus.COMPLETED);
            task.setCompletedAt(LocalDateTime.now());
            predictionTaskRepository.save(task);
            return convertToDto(savedResult);
        } catch (Exception e) {
            log.error("保存任务 {} 的预测结果（解析成功后）时出错: {}", taskId, e.getMessage(), e);
            task.setStatus(TaskStatus.FAILED);
            predictionTaskRepository.save(task);
            throw new RuntimeException("保存预测结果失败（在解析成功后）", e);
        }
    }

    /**
     * 解析 Flask API 返回的 JSON 字符串并填充 PredictionResult 实体及其关联实体。
     */
    private boolean parseAndPopulateResult(PredictionResult result, String rawJsonResponse) throws JsonProcessingException {
        JsonNode rootNode = objectMapper.readTree(rawJsonResponse);

        if (rootNode.has("error")) {
            String errorMsg = rootNode.get("error").asText("未知预测错误");
            log.warn("Flask API 响应包含错误标记: {}", errorMsg);
            result.setPredictionSummary(createErrorSummary(errorMsg));
            return false;
        }

        JsonNode predictionsNode = rootNode.path("predictions");

        if (!predictionsNode.isArray() || predictionsNode.isEmpty()) {
            log.warn("JSON 响应格式不符合预期，缺少或空的 predictions 数组。");
            result.setPredictionSummary(createErrorSummary("响应格式错误: 缺少 predictions"));
            return false;
        }

        AtomicInteger rank = new AtomicInteger(1);
        AtomicInteger wildCount = new AtomicInteger(0);
        AtomicInteger farmedCount = new AtomicInteger(0);
        List<String> distinctSpeciesNames = new ArrayList<>();
        String topLabel = null;
        BigDecimal topConfidence = BigDecimal.ZERO;

        for (int i = 0; i < predictionsNode.size(); i++) {
            JsonNode predictionDetailNode = predictionsNode.get(i);
            String classificationLabel = predictionDetailNode.path("prediction").asText();
            double confidence = predictionDetailNode.path("confidence_score").asDouble(-1.0);

            double farmedProbability = predictionDetailNode.path("farmed_probability").asDouble(0.0);
            double wildProbability = predictionDetailNode.path("wild_probability").asDouble(0.0);
            String sampleFeatures = predictionDetailNode.path("sampleFeatures").asText(null);
            String featureContributions = predictionDetailNode.path("feature_contributions").asText(null);

             if (confidence < 0 && rootNode.path("confidence_scores").isArray() && i < rootNode.path("confidence_scores").size()) {
                 confidence = rootNode.path("confidence_scores").get(i).asDouble(-1.0);
             }

            String sampleIdStr = predictionDetailNode.path("SampleID").asText(null);
            if (sampleIdStr == null) {
                sampleIdStr = String.valueOf(i);
                log.warn("JSON prediction 节点缺少 SampleID，使用索引 {} 作为字符串回退值。", i);
            }

            if (!StringUtils.hasText(classificationLabel)) {
                log.warn("跳过索引 {} 的无效预测详情记录（缺少标签）", i);
                continue;
            }

            String speciesName = extractSpeciesName(classificationLabel);
            FishSpecies species = findOrCreateFishSpecies(speciesName);
            if (species == null && !"未知".equals(speciesName)) {
                log.error("无法为物种名称 '{}' 找到或创建 FishSpecies 记录，跳过此详情记录。", speciesName);
                continue;
            }

            PredictionResultDetail detail = new PredictionResultDetail();
            detail.setSampleId(sampleIdStr); // Set String sampleId
            detail.setFishSpecies(species);
            detail.setConfidenceScore(confidence >= 0 ? confidence : null);
            detail.setClassificationLabel(classificationLabel);
            detail.setRankOrder(rank.getAndIncrement());

            detail.setFarmedProbability(farmedProbability);
            detail.setWildProbability(wildProbability);

            detail.setSampleFeatures(sampleFeatures);
            detail.setFeatureContributions(featureContributions);

            result.addResultDetail(detail);

            if (classificationLabel.startsWith("养殖")) {
                farmedCount.incrementAndGet();
            } else if (classificationLabel.startsWith("野生")) {
                wildCount.incrementAndGet();
            }

            if (speciesName != null && !"未知".equals(speciesName) && !distinctSpeciesNames.contains(speciesName)) {
                distinctSpeciesNames.add(speciesName);
            }

            if (i == 0) {
                topLabel = classificationLabel;
                if (confidence >= 0) {
                     topConfidence = BigDecimal.valueOf(confidence).setScale(9, RoundingMode.HALF_UP);
                }
            }
        }

        if (result.getResultDetails().isEmpty()) {
            log.warn("处理后没有有效的预测详情记录。");
            result.setPredictionSummary(createErrorSummary("没有有效的预测详情"));
            return false;
        }

        PredictionSummary summary = new PredictionSummary();
        logValueBeforeSetting("topClassificationLabel (from first prediction)", topLabel);
        summary.setTopClassificationLabel(truncateString(topLabel, 255));
        summary.setTopClassificationConfidence(topConfidence);
        summary.setTotalSpeciesCount(distinctSpeciesNames.size());
        summary.setWildCount(wildCount.get());
        summary.setFarmedCount(farmedCount.get());

        int totalCount = wildCount.get() + farmedCount.get();
        if (totalCount > 0) {
            summary.setWildPercentage(BigDecimal.valueOf((double)wildCount.get() * 100 / totalCount).setScale(2, RoundingMode.HALF_UP));
            summary.setFarmedPercentage(BigDecimal.valueOf((double)farmedCount.get() * 100 / totalCount).setScale(2, RoundingMode.HALF_UP));
        } else {
            summary.setWildPercentage(BigDecimal.ZERO);
            summary.setFarmedPercentage(BigDecimal.ZERO);
        }

        if (rootNode.hasNonNull("analysis_time_seconds")) {
             summary.setAnalysisTimeSeconds(rootNode.path("analysis_time_seconds").asDouble());
             log.debug("从根节点成功解析 analysis_time_seconds: {}", summary.getAnalysisTimeSeconds());
        } else {
             log.warn("JSON 响应的根节点缺少 analysis_time_seconds 字段。");
        }

        result.setPredictionSummary(summary);
        summary.setPredictionResult(result);
        if (result.getPredictionTask() != null) {
            summary.setPredictionTask(result.getPredictionTask());
        } else {
            log.warn("PredictionResult {} 没有关联的 PredictionTask，无法设置到 Summary。", result.getId());
        }

        log.info("成功解析并处理了 {} 条预测详情记录。", result.getResultDetails().size());
        return true;
    }

    private FishSpecies findOrCreateFishSpecies(String speciesName) {
        if (!StringUtils.hasText(speciesName) || "未知".equals(speciesName)) {
            log.warn("尝试查找或创建无效的物种名称: {}", speciesName);
            return null;
        }
        Optional<FishSpecies> existingSpecies = fishSpeciesRepository.findByName(speciesName);
        if (existingSpecies.isPresent()) {
            return existingSpecies.get();
        } else {
            log.info("数据库中未找到物种 {}，将创建新记录。", speciesName);
            FishSpecies newSpecies = new FishSpecies();
            newSpecies.setSpeciesName(speciesName);

             if (speciesName.startsWith("野生")) newSpecies.setOriginType("野生");
             else if (speciesName.startsWith("养殖")) newSpecies.setOriginType("养殖");
             else newSpecies.setOriginType("未知");

            try {
                return fishSpeciesRepository.save(newSpecies);
            } catch (Exception e) {
                 log.error("保存新的 FishSpecies 记录时出错，物种名称: '{}'. Error: {}", speciesName, e.getMessage(), e);
                 return null;
            }
        }
    }

     private String extractSpeciesName(String classificationLabel) {
         if (!StringUtils.hasText(classificationLabel)) {
              log.warn("extractSpeciesName 收到 null 或空标签, 返回 未知");
              return "未知";
         }
         if (classificationLabel.startsWith("野生") || classificationLabel.startsWith("养殖")) {
              if (classificationLabel.length() > 2) {
                 return classificationLabel.substring(2);
              } else {
                  log.warn("标签 {} 只有前缀没有物种名, 返回 未知", classificationLabel);
                  return "未知";
              }
         }
         return classificationLabel;
     }

    private PredictionSummary createErrorSummary(String errorMessage) {
        PredictionSummary summary = new PredictionSummary();
        String fullErrorMessage = "错误: " + (errorMessage != null ? errorMessage : "未知错误");
        logValueBeforeSetting("topClassificationLabel (error flow)", fullErrorMessage);
        summary.setTopClassificationLabel(truncateString(fullErrorMessage, 255));
        summary.setTotalSpeciesCount(0);
        summary.setWildCount(0);
        summary.setFarmedCount(0);
        summary.setWildPercentage(BigDecimal.ZERO);
        summary.setFarmedPercentage(BigDecimal.ZERO);
        summary.setTopClassificationConfidence(null);
        summary.setAnalysisTimeSeconds(null);
        return summary;
    }

    private PredictionResultDto convertToDto(PredictionResult result) {
        PredictionResultDto dto = new PredictionResultDto();
        dto.setId(result.getId());
        if (result.getPredictionTask() != null) {
            dto.setPredictionTaskId(result.getPredictionTask().getId());
        }
        dto.setCreatedAt(result.getCreatedAt());
        dto.setRawModelResponse(result.getRawModelResponse());

        if (result.getResultDetails() != null) {
            dto.setResultDetails(
                result.getResultDetails().stream()
                      .sorted(Comparator.comparing(PredictionResultDetail::getRankOrder, Comparator.nullsLast(Comparator.naturalOrder())))
                      .map(this::convertDetailToDto) // Call the updated method below
                      .collect(Collectors.toList())
            );
        } else {
            dto.setResultDetails(Collections.emptyList());
        }

        if (result.getPredictionSummary() != null) {
             dto.setPredictionSummary(convertSummaryToDto(result.getPredictionSummary()));
        } else {
             log.warn("PredictionResult ID {} 在转换为 DTO 时 PredictionSummary 为 null。", result.getId());
             dto.setPredictionSummary(null);
        }
        return dto;
    }

    //  convertDetailToDto 
    private PredictionResultDetailDto convertDetailToDto(PredictionResultDetail detail) {
        if (detail == null) {
            return null;
        }
        PredictionResultDetailDto dto = new PredictionResultDetailDto();
        dto.setId(detail.getId());
        if (detail.getFishSpecies() != null) {
            dto.setFishSpeciesId(detail.getFishSpecies().getId());
            dto.setFishSpeciesName(detail.getFishSpecies().getSpeciesName());
        }
        dto.setConfidenceScore(detail.getConfidenceScore());
        dto.setRankOrder(detail.getRankOrder());
        dto.setSampleId(detail.getSampleId());
        dto.setClassificationLabel(detail.getClassificationLabel());
        dto.setSampleFeatures(detail.getSampleFeatures());
        dto.setFeatureContributions(detail.getFeatureContributions());
        dto.setFarmedProbability(detail.getFarmedProbability());
        dto.setWildProbability(detail.getWildProbability());
        return dto;
    }

    private PredictionSummaryDTO convertSummaryToDto(PredictionSummary summary) {
        if (summary == null) {
            return null;
        }
        PredictionSummaryDTO summaryDto = new PredictionSummaryDTO();
        summaryDto.setId(summary.getId());
        summaryDto.setTopClassificationLabel(summary.getTopClassificationLabel());
        summaryDto.setTopClassificationConfidence(summary.getTopClassificationConfidence());
        summaryDto.setTotalSpeciesCount(summary.getTotalSpeciesCount());
        summaryDto.setWildCount(summary.getWildCount());
        summaryDto.setFarmedCount(summary.getFarmedCount());
        summaryDto.setWildPercentage(summary.getWildPercentage());
        summaryDto.setFarmedPercentage(summary.getFarmedPercentage());
        summaryDto.setAnalysisTimeSeconds(summary.getAnalysisTimeSeconds());
        summaryDto.setCreatedAt(summary.getCreatedAt());
        summaryDto.setUpdatedAt(summary.getUpdatedAt());

        if (summary.getPredictionResult() != null) {
            summaryDto.setPredictionResultId(summary.getPredictionResult().getId());
        }
        if (summary.getPredictionTask() != null) {
            summaryDto.setTaskId(summary.getPredictionTask().getId());
        }
        return summaryDto;
    }

    private void logValueBeforeSetting(String fieldName, String value) {
        if (value != null) {
             log.debug("尝试设置 {}，值长度: {}, 值: {}", fieldName, value.length(), value);
             if (value.length() > 255) {
                  log.warn("{} 的值超过 255 字符将被截断! 值: {}...", fieldName, value.substring(0, 250));
             }
        } else {
             log.debug("尝试设置 {} 为 null.", fieldName);
        }
    }

    private String truncateString(String value, int maxLength) {
        if (value != null && value.length() > maxLength) {
            return value.substring(0, maxLength);
        }
        return value;
    }

    // 批量处理方法 
    @Override
    @Transactional
    public PredictionResult createOrUpdateBatchPredictionResult(
            Long taskId, String rawResponse, List<Map<String, Object>> samples, String username) {

        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new EntityNotFoundException("未找到用户: " + username));
        PredictionTask task = predictionTaskRepository.findById(taskId)
                .orElseThrow(() -> new EntityNotFoundException("未找到预测任务: " + taskId));

        log.info("为任务 {} 处理批量预测结果，样本数: {}", taskId, samples != null ? samples.size() : 0);

        PredictionResult result = predictionResultRepository.findByPredictionTask_Id(taskId)
                .orElse(new PredictionResult());
        result.setPredictionTask(task);
        result.setRawModelResponse(rawResponse);

        result.getResultDetails().clear();
        if (result.getPredictionSummary() != null) {
             result.getPredictionSummary().setPredictionResult(null);
             result.setPredictionSummary(null);
        }

        try {
            JsonNode rootNode = objectMapper.readTree(rawResponse);
            int wildCount = 0;
            int farmedCount = 0;
            double totalConfidence = 0;
            int validSamplesProcessed = 0;
            List<String> distinctBatchSpeciesNames = new ArrayList<>();

            JsonNode resultsNode = rootNode.path("results");
            if (resultsNode.isArray() && !resultsNode.isEmpty()) {
                log.info("发现 'results' 数组，包含 {} 个元素，开始处理...", resultsNode.size());
                for (int i = 0; i < resultsNode.size(); i++) {
                    JsonNode sampleResult = resultsNode.get(i);
                    String label = sampleResult.path("prediction").asText("未知");
                    double confidence = sampleResult.path("confidence_score").asDouble(0);

                    // *** 修改: 读取 farmed 和 wild probability (批量) ***
                    double farmedProbability = sampleResult.path("farmed_probability").asDouble(0.0);
                    double wildProbability = sampleResult.path("wild_probability").asDouble(0.0);
                    String sampleFeatures = sampleResult.path("sampleFeatures").asText(null);
                    String featureContributionsJson = sampleResult.path("feature_contributions").asText(null);
                    String sampleIdStr = sampleResult.path("sample_id").asText(String.valueOf(i));

                    if ("未知".equals(label) && confidence == 0) {
                         log.warn("跳过批量结果索引 {}：标签为 '未知' 且置信度为 0。", i);
                         continue;
                    }

                    PredictionResultDetail detail = new PredictionResultDetail();
                    detail.setSampleId(sampleIdStr); // Set String sampleId
                    detail.setConfidenceScore(confidence);
                    detail.setClassificationLabel(label);
                    detail.setRankOrder(i + 1);

                    // *** 修改: 设置 farmed 和 wild probability (批量) ***
                    detail.setFarmedProbability(farmedProbability);
                    detail.setWildProbability(wildProbability);
                    // *** 移除: 设置单一 probability 的逻辑 ***

                    detail.setSampleFeatures(sampleFeatures);
                    detail.setFeatureContributions(featureContributionsJson);

                    String speciesName = extractSpeciesName(label);
                    FishSpecies fishSpecies = findOrCreateFishSpecies(speciesName);
                    if (fishSpecies == null && !"未知".equals(speciesName)) {
                        log.error("无法为物种名称 '{}' 找到或创建 FishSpecies 记录（批量），跳过此详情记录。", speciesName);
                        continue;
                    }
                    detail.setFishSpecies(fishSpecies);

                    result.addResultDetail(detail);
                    validSamplesProcessed++;
                    totalConfidence += confidence;

                    // Update wild/farmed counts
                     if (label.startsWith("养殖")) farmedCount++;
                     else if (label.startsWith("野生")) wildCount++;

                    if (speciesName != null && !"未知".equals(speciesName) && !distinctBatchSpeciesNames.contains(speciesName)) {
                        distinctBatchSpeciesNames.add(speciesName);
                    }
                }
                log.info("批量 'results' 数组处理完成，有效样本数: {}", validSamplesProcessed);

            } else {
                // Fallback for single result
                log.warn("响应中没有发现 'results' 数组或数组为空，尝试解析根节点作为单个结果。");
                String label = rootNode.path("prediction").asText("未知");
                double confidence = rootNode.path("confidence_score").asDouble(0);

                // *** 修改: 读取 probability (单结果模式) ***
                double farmedProbability = rootNode.path("farmed_probability").asDouble(0.0);
                double wildProbability = rootNode.path("wild_probability").asDouble(0.0);
                String sampleFeatures = rootNode.path("sampleFeatures").asText(null);
                String featureContributionsJson = rootNode.path("feature_contributions").asText(null);
                String sampleIdStr = rootNode.path("SampleID").asText("0");

                if (!"未知".equals(label) || confidence != 0) {
                    PredictionResultDetail detail = new PredictionResultDetail();
                    detail.setConfidenceScore(confidence);
                    detail.setClassificationLabel(label);
                    detail.setRankOrder(1);
                    detail.setSampleId(sampleIdStr); // Set String sampleId

                    // *** 修改: 设置 farmed 和 wild probability (单结果模式) ***
                    detail.setFarmedProbability(farmedProbability);
                    detail.setWildProbability(wildProbability);
                    // *** 移除: 设置单一 probability 的逻辑 ***

                    detail.setSampleFeatures(sampleFeatures);
                    detail.setFeatureContributions(featureContributionsJson);

                    String speciesName = extractSpeciesName(label);
                    FishSpecies fishSpecies = findOrCreateFishSpecies(speciesName);
                     if (fishSpecies == null && !"未知".equals(speciesName)) {
                         log.error("无法为物种名称 '{}' 找到或创建 FishSpecies 记录（单结果模式），处理失败。", speciesName);
                         result.setPredictionSummary(createErrorSummary("无法处理物种: " + speciesName));
                         PredictionResult savedResultWithError = predictionResultRepository.save(result);
                         task.setStatus(TaskStatus.FAILED);
                         predictionTaskRepository.save(task);
                         return savedResultWithError;
                     }
                    detail.setFishSpecies(fishSpecies);
                    result.addResultDetail(detail);
                    validSamplesProcessed = 1;

                     if (label.startsWith("养殖")) { farmedCount = 1; wildCount = 0; }
                     else if (label.startsWith("野生")) { wildCount = 1; farmedCount = 0; }
                     else { wildCount = 0; farmedCount = 0; }
                     totalConfidence = confidence;
                     if (speciesName != null && !"未知".equals(speciesName)) {
                         distinctBatchSpeciesNames.add(speciesName);
                     }
                     log.info("已将根节点解析为单个有效结果。");
                } else {
                     log.error("无法从响应中解析任何有效的预测结果（既无 'results' 数组，根节点也无效）。");
                     result.setPredictionSummary(createErrorSummary("无法解析预测结果"));
                     PredictionResult savedResultWithError = predictionResultRepository.save(result);
                     task.setStatus(TaskStatus.FAILED);
                     predictionTaskRepository.save(task);
                     return savedResultWithError;
                }
            }

             if (validSamplesProcessed == 0) {
                 log.error("处理完成，但没有有效的预测样本被处理。");
                 result.setPredictionSummary(createErrorSummary("无有效预测样本"));
                 PredictionResult savedResultWithError = predictionResultRepository.save(result);
                 task.setStatus(TaskStatus.FAILED);
                 predictionTaskRepository.save(task);
                 return savedResultWithError;
             }

            double avgConfidence = validSamplesProcessed > 0 ? totalConfidence / validSamplesProcessed : 0;
            PredictionSummary summary = new PredictionSummary();
            summary.setWildCount(wildCount);
            summary.setFarmedCount(farmedCount);
            int totalSamplesCount = wildCount + farmedCount;
            if (totalSamplesCount > 0) {
                summary.setWildPercentage(BigDecimal.valueOf((double)wildCount * 100 / totalSamplesCount).setScale(2, RoundingMode.HALF_UP));
                summary.setFarmedPercentage(BigDecimal.valueOf((double)farmedCount * 100 / totalSamplesCount).setScale(2, RoundingMode.HALF_UP));
            } else {
                summary.setWildPercentage(BigDecimal.ZERO);
                summary.setFarmedPercentage(BigDecimal.ZERO);
            }

             String batchTopLabel = "批量处理";
             if (!result.getResultDetails().isEmpty()) {
                 // batchTopLabel = result.getResultDetails().get(0).getClassificationLabel();
             }
             logValueBeforeSetting("topClassificationLabel (batch flow)", batchTopLabel);
             summary.setTopClassificationLabel(truncateString(batchTopLabel, 255));
            summary.setTopClassificationConfidence(BigDecimal.valueOf(avgConfidence).setScale(9, RoundingMode.HALF_UP));
            summary.setTotalSpeciesCount(distinctBatchSpeciesNames.size());

            if (rootNode.hasNonNull("analysis_time_seconds")) {
                summary.setAnalysisTimeSeconds(rootNode.path("analysis_time_seconds").asDouble());
            }

            result.setPredictionSummary(summary);
            summary.setPredictionResult(result);
            if (task != null) {
                summary.setPredictionTask(task);
            } else {
                log.error("批量处理 Task ID {} 时，Task 对象为 null，无法设置 Summary 关联！", taskId);
            }

            PredictionResult savedResult = predictionResultRepository.save(result);
            log.info("批量预测结果已成功保存: ID={}, 任务ID={}", savedResult.getId(), taskId);
            task.setStatus(TaskStatus.COMPLETED);
            task.setCompletedAt(LocalDateTime.now());
            predictionTaskRepository.save(task);
            return savedResult;

        } catch (Exception e) {
            log.error("处理批量预测结果时发生意外错误, Task ID {}: {}", taskId, e.getMessage(), e);
            try {
                result.setPredictionSummary(createErrorSummary("处理批量结果错误: " + e.getMessage()));
                if (result.getPredictionSummary() != null) {
                    result.getPredictionSummary().setPredictionResult(result);
                    if (task != null) {
                        result.getPredictionSummary().setPredictionTask(task);
                    }
                }
                PredictionResult savedResultWithError = predictionResultRepository.save(result);
                task.setStatus(TaskStatus.FAILED);
                predictionTaskRepository.save(task);
                return savedResultWithError;
            } catch (Exception saveEx) {
                log.error("保存批量预测错误结果时出错, Task ID {}: {}", taskId, saveEx.getMessage(), saveEx);
                task.setStatus(TaskStatus.FAILED);
                predictionTaskRepository.save(task);
                throw new RuntimeException("无法保存批量预测结果", saveEx);
            }
        }
    }

    @Override
    public PredictionResultDto getPredictionResultByTaskId(Long taskId, String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new EntityNotFoundException("未找到用户: " + username));
        PredictionTask task = predictionTaskRepository.findById(taskId)
                .orElseThrow(() -> new EntityNotFoundException("未找到预测任务: " + taskId));
        if (!task.getUser().getId().equals(user.getId())) {
             log.warn("用户 {} (ID:{}) 尝试访问不属于自己的任务 {} 的结果 (所有者ID:{})",
                      username, user.getId(), taskId, task.getUser().getId());
            throw new SecurityException("您无权访问此预测任务的结果");
        }
        PredictionResult result = predictionResultRepository.findByPredictionTask_Id(taskId)
                .orElseThrow(() -> new EntityNotFoundException("未找到预测任务的结果: " + taskId));
        return convertToDto(result);
    }

    @Override
    public PredictionResultDto getPredictionResultById(Long resultId, String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new EntityNotFoundException("未找到用户: " + username));
        PredictionResult result = predictionResultRepository.findById(resultId)
                .orElseThrow(() -> new EntityNotFoundException("未找到预测结果: " + resultId));
        if (result.getPredictionTask() == null || result.getPredictionTask().getUser() == null ||
            !result.getPredictionTask().getUser().getId().equals(user.getId())) {
             log.warn("用户 {} (ID:{}) 尝试访问预测结果 {}，该结果不属于该用户或关联任务/用户丢失",
                      username, user.getId(), resultId);
            throw new SecurityException("您无权访问此预测结果");
        }
        return convertToDto(result);
    }

    @Transactional
    public PredictionResult savePredictionResult(Long taskId, String rawResponse) {
        log.info("内部调用：保存任务 {} 的预测结果。", taskId);
        PredictionTask task = predictionTaskRepository.findById(taskId)
                .orElseThrow(() -> new EntityNotFoundException("内部错误：找不到任务: " + taskId));

        PredictionResult result = predictionResultRepository.findByPredictionTask_Id(taskId)
                .orElse(new PredictionResult());
        result.setPredictionTask(task);
        result.setRawModelResponse(rawResponse);

        result.getResultDetails().clear();
        if (result.getPredictionSummary() != null) {
             result.getPredictionSummary().setPredictionResult(null);
             result.setPredictionSummary(null);
        }

        try {
            boolean success = parseAndPopulateResult(result, rawResponse);
             if (!success) {
                  log.warn("内部调用 savePredictionResult 时，解析任务 {} 响应失败。", taskId);
                  if (result.getPredictionSummary() == null) {
                      result.setPredictionSummary(createErrorSummary("内部解析失败"));
                  }
                  result.getPredictionSummary().setPredictionResult(result);
                  result.getPredictionSummary().setPredictionTask(task);
                  return predictionResultRepository.save(result);
             }
        } catch (Exception e) {
            log.error("内部调用 savePredictionResult 解析任务 {} 响应时出错: {}", taskId, e.getMessage(), e);
             try {
                 result.setPredictionSummary(createErrorSummary("内部解析错误: " + e.getMessage()));
                 result.getPredictionSummary().setPredictionResult(result);
                 result.getPredictionSummary().setPredictionTask(task);
                 return predictionResultRepository.save(result);
             } catch (Exception saveEx) {
                  log.error("内部调用 savePredictionResult 保存错误结果时出错: {}", taskId, saveEx.getMessage(), saveEx);
                  throw new RuntimeException("无法保存内部预测结果", saveEx);
             }
        }

        if (result.getPredictionSummary() != null) {
             if (result.getPredictionSummary().getPredictionResult() == null) {
                 result.getPredictionSummary().setPredictionResult(result);
             }
             if (result.getPredictionSummary().getPredictionTask() == null) {
                 result.getPredictionSummary().setPredictionTask(task);
             }
        }

        PredictionResult savedResult = predictionResultRepository.save(result);
        log.info("内部调用 savePredictionResult 成功保存结果: ID={}", savedResult.getId());
        return savedResult;
    }

    @Override
    public List<PredictionResultDetailDto> getPredictionResultDetailsByTaskId(Long taskId, String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new EntityNotFoundException("未找到用户: " + username));
        PredictionTask task = predictionTaskRepository.findById(taskId)
                .orElseThrow(() -> new EntityNotFoundException("未找到预测任务: " + taskId));
        if (!task.getUser().getId().equals(user.getId())) {
            log.warn("用户 {} (ID:{}) 尝试访问不属于自己的任务 {} 的详情 (所有者ID:{})",
                     username, user.getId(), taskId, task.getUser().getId());
            throw new SecurityException("您无权访问此预测任务的结果详情");
        }
        PredictionResult result = predictionResultRepository.findByPredictionTask_Id(taskId)
                .orElseThrow(() -> new EntityNotFoundException("未找到预测任务的结果: " + taskId));

        return result.getResultDetails().stream()
                .sorted(Comparator.comparing(PredictionResultDetail::getRankOrder, Comparator.nullsLast(Comparator.naturalOrder())))
                .map(this::convertDetailToDto) // Uses the updated converter
                .collect(Collectors.toList());
    }

    private void publishTaskStatusChangeEvent(Long taskId, String status, String username) {
        // This method is intentionally left empty to prevent duplicate status updates.
        // The status is now set directly in the calling method.
        log.warn("publishTaskStatusChangeEvent is deprecated. Status for task {} should be set directly. Status: {}", taskId, status);
    }
}
