import os
import pandas as pd
import numpy as np
from flask import Flask, request, jsonify
from flask_cors import CORS
import logging
import shap
import torch
import io
import pickle
from functools import partial
import torch.nn as nn
import time
import json

# 配置日志
logging.basicConfig(level=logging.INFO)


# PATCH 1: Force CPU during deserialization (loading)
logging.info("[PATCH 1/4] Applying global torch.load patch to force CPU-mapping during deserialization.")
torch.load = partial(torch.load, map_location=torch.device('cpu'))


# PATCH 2: Force CPU when moving the model itself
logging.info("[PATCH 2/4] Applying global nn.Module.to patch to intercept and force CPU for models.")
original_module_to = nn.Module.to
def patched_module_to(self, *args, **kwargs):
    modified_args = list(args); modified_kwargs = kwargs.copy()
    try:
        device_arg = modified_kwargs.get('device') or (modified_args[0] if len(modified_args) > 0 else None)
        if device_arg and 'cuda' in str(device_arg):
            logging.warning(f"MODEL PATCH: Intercepted a .to('{device_arg}') call for a module and forcing it to 'cpu'.")
            if 'device' in modified_kwargs: modified_kwargs['device'] = 'cpu'
            else: modified_args[0] = 'cpu'
    except Exception as e: logging.error(f"Error in patched_module_to: {e}.")
    return original_module_to(self, *modified_args, **modified_kwargs)
nn.Module.to = patched_module_to


# PATCH 3: Force CPU when moving data tensors
logging.info("[PATCH 3/4] Applying global torch.Tensor.to patch to intercept and force CPU for data.")
original_tensor_to = torch.Tensor.to
def patched_tensor_to(self, *args, **kwargs):
    modified_args = list(args); modified_kwargs = kwargs.copy()
    try:
        device_arg = modified_kwargs.get('device') or (modified_args[0] if len(modified_args) > 0 else None)
        if device_arg and 'cuda' in str(device_arg):
            logging.warning(f"TENSOR PATCH: Intercepted a .to('{device_arg}') call for a tensor and forcing it to 'cpu'.")
            if 'device' in modified_kwargs: modified_kwargs['device'] = 'cpu'
            else: modified_args[0] = 'cpu'
    except Exception as e: logging.error(f"Error in patched_tensor_to: {e}.")
    return original_tensor_to(self, *modified_args, **modified_kwargs)
torch.Tensor.to = patched_tensor_to


# PATCH 4: Deceive hardware property checks (The Final Boss)
logging.info("[PATCH 4/4] Applying global torch.cuda.get_device_properties patch to fake GPU hardware.")
if hasattr(torch, 'cuda'):
    class MockCudaDeviceProperties:
        def __init__(self): self.total_memory = 16 * 1024 * 1024 * 1024 # Fake 16GB VRAM
    def patched_get_device_properties(device):
        logging.warning("HARDWARE PATCH: Intercepted torch.cuda.get_device_properties. Returning mock CPU memory stats.")
        return MockCudaDeviceProperties()
    torch.cuda.get_device_properties = patched_get_device_properties


# joblib is now imported AFTER all patches have been applied.
import joblib


app = Flask(__name__)
CORS(app)

# 1. 更改鱼种的特征定义
DEFAULT_FEATURES = {
    'lianyu': [
        'p__Bacteroidota', 'c__Coriobacteriia', 'o__Mycobacteriales',
        'f__Casimicrobiaceae', 'g__Methylosinus', 's__Cetobacterium_A somerae'
    ],
    'yongyu': [
        'p__Fusobacteriota', 'c__Actinomycetia', 'o__Ardenticatenales',
        'f__Microtrichaceae', 'g__Nocardioides_B', 's__Caldilinea tarbellica'
    ]
}

class ModelManager:
    def __init__(self, species_name, model_path):
        self.species_name = species_name
        self.model = None
        self.encoder = None
        self.load_models(model_path)

    def load_models(self, model_path):
        self._load_model(model_path)
        self._load_encoder(model_path)

    def _load_model(self, model_path):
        try:
            model_file = os.path.join(model_path, f'{self.species_name}_model.joblib')
            self.model = joblib.load(model_file)
            logging.info(f'Model for {self.species_name} loaded successfully from {model_file}')
        except FileNotFoundError:
            logging.error(f'Model file for {self.species_name} not found at {model_path}')
        except Exception as e:
            logging.error(f'Error loading model for {self.species_name}: {e}', exc_info=True)

    def _load_encoder(self, model_path):
        try:
            encoder_file = os.path.join(model_path, f'{self.species_name}_encoder.joblib')
            self.encoder = joblib.load(encoder_file)
            logging.info(f'Encoder for {self.species_name} loaded from {encoder_file}')
        except FileNotFoundError:
            logging.warning(f'Encoder file for {self.species_name} not found, some functionalities might be limited.')
        except Exception as e:
            logging.error(f'Error loading encoder for {self.species_name}: {e}')

class SimpleModelPredictor:
    def __init__(self, model, encoder):
        self.model = model
        self.encoder = encoder

    def preprocess_data(self, data, features):
        for feature in features:
            if feature not in data.columns:
                data[feature] = 0
        return data[features]

    def predict(self, data):
        # 恢复的逻辑：返回一个包含所有原始预测信息的DataFrame
        logging.info(f"Performing raw prediction for {len(data)} samples.")
        
        # 检查模型是否支持 predict_proba
        if not hasattr(self.model, "predict_proba"):
            logging.error("Model does not support predict_proba, cannot proceed.")
            # 返回一个空的或带有错误标记的DataFrame
            results_df = pd.DataFrame(index=data.index)
            results_df['error'] = "Model does not support probabilities"
            return results_df

        probabilities = self.model.predict_proba(data)
        predictions_encoded = np.argmax(probabilities, axis=1)
        confidence_scores = np.max(probabilities, axis=1)

        results_df = pd.DataFrame(index=data.index)
        results_df['prediction_encoded'] = predictions_encoded
        results_df['confidence_score'] = confidence_scores
        
        # 解码英文标签
        if self.encoder:
            try:
                results_df['prediction_english'] = self.encoder.inverse_transform(predictions_encoded)
            except Exception as e:
                logging.error(f"Error during inverse_transform: {e}")
                results_df['prediction_english'] = "Error decoding"
        else:
            results_df['prediction_english'] = "Encoder not available"

        logging.info("Raw prediction complete. Returning DataFrame for full processing in route.")
        return results_df

model_managers = {}

def setup_models():
    base_model_path = os.environ.get('MODEL_PATH', 'models')
    supported_species = ['lianyu', 'yongyu']
    for species in supported_species:
        species_path = os.path.join(base_model_path, species)
        if os.path.isdir(species_path):
            model_managers[species] = ModelManager(species, species_path)
        else:
            logging.warning(f"Directory for species '{species}' not found at '{species_path}'")

@app.route('/predict', methods=['POST'])
def predict_endpoint():
    request_start_time = time.time()
    if 'file' not in request.files:
        return jsonify({'error': 'No file part'}), 400
    file = request.files['file']
    species = request.form.get('fish_type')
    if not species:
        return jsonify({'error': 'Missing species parameter'}), 400
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400
    try:
        data = pd.read_excel(file, index_col=0)
    except Exception as e:
        return jsonify({'error': f"Error reading Excel file: {str(e)}"}), 400
    
    manager = model_managers.get(species)
    if not manager or not manager.model:
        return jsonify({'error': f'Model for {species} not loaded or failed to load'}), 500
    
    features = DEFAULT_FEATURES.get(species, [])
    predictor = SimpleModelPredictor(manager.model, manager.encoder)
    preprocessed_data = predictor.preprocess_data(data.copy(), features)
    
    if preprocessed_data.empty:
        return jsonify({'error': 'No valid data to predict after preprocessing. Check if column names match features.'}), 400
    
    try:
        # 完整逻辑 
        request_start_time = time.time()
        
        # 1. 执行预测
        results_df = predictor.predict(preprocessed_data)
        if 'error' in results_df.columns:
             return jsonify({'error': results_df['error'].iloc[0]}), 500

        # 2. 准备映射规则
        LABEL_MAP = {
            "WL": "野生鲢鱼",
            "CL": "养殖鲢鱼",
            "WY": "野生鳙鱼",
            "CY": "养殖鳙鱼"
        }

        # 3. 循环处理每个样本，应用新规则
        predictions_list = []
        # 将原始数据转换为字典以便快速查找
        original_features_dict = data.to_dict(orient='index')
        # 重置索引以获得SampleID列
        results_df_reset = results_df.reset_index().rename(columns={'index': 'SampleID'})

        for record in results_df_reset.to_dict(orient='records'):
            english_label = record.get("prediction_english", "")
            confidence = record.get("confidence_score")

            # 根据英文标签分配概率
            wild_prob = None
            farmed_prob = None
            if confidence is not None:
                if "W" in english_label:
                    wild_prob = float(confidence)
                    farmed_prob = 1.0 - wild_prob
                elif "C" in english_label:
                    farmed_prob = float(confidence)
                    wild_prob = 1.0 - farmed_prob
            
            # 翻译成中文标签
            chinese_label = LABEL_MAP.get(english_label, "未知标签")

            pred_dict = {
                "SampleID": record.get("SampleID"),
                "prediction": chinese_label,
                "confidence_score": float(confidence) if pd.notna(confidence) else None,
                "wild_probability": float(wild_prob) if pd.notna(wild_prob) else None,
                "farmed_probability": float(farmed_prob) if pd.notna(farmed_prob) else None
            }

            # 添加 sampleFeatures
            sample_id = pred_dict.get('SampleID')
            if sample_id in original_features_dict:
                feature_dict = original_features_dict[sample_id]
                try:
                    serializable_features = {k: (v.item() if isinstance(v, np.generic) else v) for k, v in feature_dict.items()}
                    pred_dict['sampleFeatures'] = json.dumps(serializable_features, ensure_ascii=False, allow_nan=False)
                except Exception as e:
                    logging.error(f"Error serializing features for SampleID {sample_id}: {e}")
                    pred_dict['sampleFeatures'] = json.dumps({"error": "特征序列化失败"}, ensure_ascii=False)
            else:
                pred_dict['sampleFeatures'] = None
            
            predictions_list.append(pred_dict)

        # 4. 恢复并实现 summary 计算
        wild_count = sum(1 for p in predictions_list if p['prediction'].startswith('野生'))
        farmed_count = sum(1 for p in predictions_list if p['prediction'].startswith('养殖'))
        total_samples = len(predictions_list)
        
        # 计算平均置信度
        all_confidences = [p['confidence_score'] for p in predictions_list if p['confidence_score'] is not None]
        
        # 使用更健壮的方式计算，防止任何潜在的类型问题
        if all_confidences:
            # 确保所有加数和除数都是python float，然后执行浮点除法
            average_confidence = sum(float(c) for c in all_confidences) / float(len(all_confidences))
        else:
            average_confidence = None

        summary_obj = {
            "fish_type": species,
            "total_samples": total_samples,
            "farmed_count": farmed_count,
            "wild_count": wild_count,
            # 显式转换为浮点数以保证除法正确
            "farmed_ratio": float(farmed_count) / total_samples if total_samples > 0 else 0.0,
            "wild_ratio": float(wild_count) / total_samples if total_samples > 0 else 0.0,
            "average_confidence": average_confidence
        }
        
        total_duration = time.time() - request_start_time

        # 5. 最终响应
        response_data = {
            "predictions": predictions_list,
            "summary": summary_obj,
            "analysis_time_seconds": total_duration
        }
        
        logging.info("Prediction request processed successfully using new mapping logic.")
        return jsonify(response_data), 200

    except Exception as e:
        request_end_time = time.time()
        total_duration = request_end_time - request_start_time
        logging.error(f"An uncaught error occurred after {total_duration:.4f} seconds: {e}", exc_info=True)
        return jsonify({'error': 'An internal server error occurred', 'details': str(e)}), 500

# Run setup at module level
setup_models()

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)