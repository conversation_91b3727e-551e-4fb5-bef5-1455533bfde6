cat > /var/www/api/fish-api/src/main/java/com/example/fishapi/service/EmailService.java << EOF
package com.example.fishapi.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.MailException;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

@Service
public class EmailService {

    private static final Logger log = LoggerFactory.getLogger(EmailService.class);

    @Autowired
    private JavaMailSender mailSender;

    @Value("\${mail.from.address}") // 从配置文件读取发件人地址
    private String fromAddress;

    public void sendVerificationCode(String toEmail, String code) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromAddress);
            message.setTo(toEmail);
            message.setSubject("Your Password Reset Code");
            message.setText("Your password reset verification code is: " + code + "\n" +
                           "This code will expire in 10 minutes."); // 可以提示过期时间

            mailSender.send(message);
            log.info("Verification code sent successfully to {}", toEmail);
        } catch (MailException e) {
            log.error("Error sending verification code to {}: {}", toEmail, e.getMessage());
            // 根据需要处理异常，例如抛出自定义异常
            // throw new EmailSendingException("Failed to send verification code", e);
        }
    }
}
EOF 