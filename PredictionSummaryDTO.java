# --- 开始替换 PredictionSummaryDTO.java (修正 toString) ---
sudo bash -c 'cat > /var/www/api/fish-api/src/main/java/com/example/fishapi/dto/PredictionSummaryDTO.java << "EOF"
package com.example.fishapi.dto;

// Removed lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

// Removed @Data from outer and inner class
@NoArgsConstructor
@AllArgsConstructor
public class PredictionSummaryDTO {
    private Long id;
    private Long predictionResultId; // Added based on model relationship
    private Long taskId; // Added based on model relationship
    private String topClassificationLabel;
    private BigDecimal topClassificationConfidence;
    private Integer totalSpeciesCount;
    private Integer wildCount;
    private Integer farmedCount;
    private BigDecimal wildPercentage;
    private BigDecimal farmedPercentage;
    private Double analysisTimeSeconds;
    private List<DetailedResult> detailedResults; // Field was present in DTO
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt; // Added based on model

    // --- Manually added Getters and Setters for outer class ---

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPredictionResultId() {
        return predictionResultId;
    }

    public void setPredictionResultId(Long predictionResultId) {
        this.predictionResultId = predictionResultId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getTopClassificationLabel() {
        return topClassificationLabel;
    }

    public void setTopClassificationLabel(String topClassificationLabel) {
        this.topClassificationLabel = topClassificationLabel;
    }

    public BigDecimal getTopClassificationConfidence() {
        return topClassificationConfidence;
    }

    public void setTopClassificationConfidence(BigDecimal topClassificationConfidence) {
        this.topClassificationConfidence = topClassificationConfidence;
    }

    public Integer getTotalSpeciesCount() {
        return totalSpeciesCount;
    }

    public void setTotalSpeciesCount(Integer totalSpeciesCount) {
        this.totalSpeciesCount = totalSpeciesCount;
    }

    public Integer getWildCount() {
        return wildCount;
    }

    public void setWildCount(Integer wildCount) {
        this.wildCount = wildCount;
    }

    public Integer getFarmedCount() {
        return farmedCount;
    }

    public void setFarmedCount(Integer farmedCount) {
        this.farmedCount = farmedCount;
    }

    public BigDecimal getWildPercentage() {
        return wildPercentage;
    }

    public void setWildPercentage(BigDecimal wildPercentage) {
        this.wildPercentage = wildPercentage;
    }

    public BigDecimal getFarmedPercentage() {
        return farmedPercentage;
    }

    public void setFarmedPercentage(BigDecimal farmedPercentage) {
        this.farmedPercentage = farmedPercentage;
    }

    public Double getAnalysisTimeSeconds() {
        return analysisTimeSeconds;
    }

    public void setAnalysisTimeSeconds(Double analysisTimeSeconds) {
        this.analysisTimeSeconds = analysisTimeSeconds;
    }

    public List<DetailedResult> getDetailedResults() {
        return detailedResults;
    }

    public void setDetailedResults(List<DetailedResult> detailedResults) {
        this.detailedResults = detailedResults;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    // --- equals(), hashCode(), toString() for outer class ---

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PredictionSummaryDTO that = (PredictionSummaryDTO) o;
        return Objects.equals(id, that.id) && Objects.equals(predictionResultId, that.predictionResultId) && Objects.equals(taskId, that.taskId) && Objects.equals(topClassificationLabel, that.topClassificationLabel) && Objects.equals(topClassificationConfidence, that.topClassificationConfidence) && Objects.equals(totalSpeciesCount, that.totalSpeciesCount) && Objects.equals(wildCount, that.wildCount) && Objects.equals(farmedCount, that.farmedCount) && Objects.equals(wildPercentage, that.wildPercentage) && Objects.equals(farmedPercentage, that.farmedPercentage) && Objects.equals(analysisTimeSeconds, that.analysisTimeSeconds) && Objects.equals(detailedResults, that.detailedResults) && Objects.equals(createdAt, that.createdAt) && Objects.equals(updatedAt, that.updatedAt);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, predictionResultId, taskId, topClassificationLabel, topClassificationConfidence, totalSpeciesCount, wildCount, farmedCount, wildPercentage, farmedPercentage, analysisTimeSeconds, detailedResults, createdAt, updatedAt);
    }

    // --- toString() - Corrected: Removed problematic single quotes ---
    @Override
    public String toString() {
        return "PredictionSummaryDTO{" +
               "id=" + id +
               ", predictionResultId=" + predictionResultId +
               ", taskId=" + taskId +
               ", topClassificationLabel=" + topClassificationLabel + // Removed quotes
               ", wildCount=" + wildCount +
               ", farmedCount=" + farmedCount +
               ", createdAt=" + createdAt +
               '}';
    }

    // --- Inner static class DetailedResult ---

    @NoArgsConstructor
    @AllArgsConstructor
    public static class DetailedResult {
        private String species;
        private String classification;
        private BigDecimal probability;
        private BigDecimal confidence;

        // --- Manually added Getters and Setters for inner class ---

        public String getSpecies() {
            return species;
        }

        public void setSpecies(String species) {
            this.species = species;
        }

        public String getClassification() {
            return classification;
        }

        public void setClassification(String classification) {
            this.classification = classification;
        }

        public BigDecimal getProbability() {
            return probability;
        }

        public void setProbability(BigDecimal probability) {
            this.probability = probability;
        }

        public BigDecimal getConfidence() {
            return confidence;
        }

        public void setConfidence(BigDecimal confidence) {
            this.confidence = confidence;
        }

        // --- equals(), hashCode(), toString() for inner class ---

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            DetailedResult that = (DetailedResult) o;
            return Objects.equals(species, that.species) && Objects.equals(classification, that.classification) && Objects.equals(probability, that.probability) && Objects.equals(confidence, that.confidence);
        }

        @Override
        public int hashCode() {
            return Objects.hash(species, classification, probability, confidence);
        }

        // --- toString() - Corrected: Removed problematic single quotes ---
        @Override
        public String toString() {
            return "DetailedResult{" +
                   "species=" + species + // Removed quotes
                   ", classification=" + classification + // Removed quotes
                   ", probability=" + probability +
                   ", confidence=" + confidence +
                   '}';
        }
    }
}
EOF'
# --- 结束替换 PredictionSummaryDTO.java ---