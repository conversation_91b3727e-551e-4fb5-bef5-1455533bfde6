cat /var/www/api/fish-api/src/main/java/com/example/fishapi/config/PasswordEncoderConfig.java
    package com.example.fishapi.config;

    import org.springframework.context.annotation.Bean;
    import org.springframework.context.annotation.Configuration;
    import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
    import org.springframework.security.crypto.password.PasswordEncoder;

    @Configuration
    public class PasswordEncoderConfig {

        @Bean
        public PasswordEncoder passwordEncoder() {
            // 仅在此处定义 PasswordEncoder Bean
            return new BCryptPasswordEncoder();
        }
    }
admin@lavm-zam5kdzhoo:~$     cat /var/www/api/fish-api/src/main/java/com/example/fishapi/config/PasswordEncoderConfig.java
    package com.example.fishapi.config;

    import org.springframework.context.annotation.Bean;
    import org.springframework.context.annotation.Configuration;
    import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
    import org.springframework.security.crypto.password.PasswordEncoder;

    @Configuration
    public class PasswordEncoderConfig {

        @Bean
        public PasswordEncoder passwordEncoder() {
            // 仅在此处定义 PasswordEncoder Bean
            return new BCryptPasswordEncoder();
        }
    }