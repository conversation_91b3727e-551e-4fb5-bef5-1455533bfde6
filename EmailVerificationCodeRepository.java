# 创建 EmailVerificationCodeRepository.java
cat << EOF > /var/www/api/fish-api/src/main/java/com/example/fishapi/repository/EmailVerificationCodeRepository.java
package com.example.fishapi.repository;

import com.example.fishapi.model.EmailVerificationCode;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;

@Repository
public interface EmailVerificationCodeRepository extends JpaRepository<EmailVerificationCode, Long> {

    // 查找最新的、未过期的验证码
    Optional<EmailVerificationCode> findFirstByEmailAndExpiryTimeAfterOrderByExpiryTimeDesc(String email, LocalDateTime now);

    // 删除指定邮箱的所有验证码
    @Transactional // 删除操作建议加上事务注解
    void deleteAllByEmail(String email);

    // 用于定时任务清理过期验证码
    @Transactional
    void deleteAllByExpiryTimeBefore(LocalDateTime now);
}
EOF
echo "EmailVerificationCodeRepository.java created."