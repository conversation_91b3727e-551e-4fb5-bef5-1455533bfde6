//  sudo bash -c 'cat << EOF > /var/www/api/fish-api/src/main/java/com/example/fishapi/service/impl/ExportServiceImpl.java
package com.example.fishapi.service.impl;

import com.example.fishapi.dto.PredictionResultDetailDto;
import com.example.fishapi.model.PredictionTask;
import com.example.fishapi.model.User;
import com.example.fishapi.repository.PredictionTaskRepository;
import com.example.fishapi.repository.UserRepository;
import com.example.fishapi.service.ExportService;
import com.example.fishapi.service.PredictionResultService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityNotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ExportServiceImpl implements ExportService {

    @Autowired
    private PredictionResultService predictionResultService;

    @Autowired
    private PredictionTaskRepository predictionTaskRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public ByteArrayOutputStream exportPredictionResultsToExcel(Long taskId, String username) {
        log.info("开始为任务 ID {} 导出预测结果到 Excel (用户: {})", taskId, username);
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new EntityNotFoundException("未找到用户: " + username));

        PredictionTask task = predictionTaskRepository.findById(taskId)
                .orElseThrow(() -> new EntityNotFoundException("未找到预测任务: " + taskId));

        if (!task.getUser().getId().equals(user.getId())) {
            log.warn("用户 {} (ID:{}) 尝试导出不属于自己的任务 {} 的结果 (所有者ID:{})",
                     username, user.getId(), taskId, task.getUser().getId());
            throw new SecurityException("您无权导出此预测任务的结果");
        }

        List<PredictionResultDetailDto> details = predictionResultService.getPredictionResultDetailsByTaskId(taskId, username);

        try (Workbook workbook = new XSSFWorkbook(); ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            Sheet sheet = workbook.createSheet("预测结果");

            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);

            // --- 动态列生成 ---
            List<String> featureContributionColumns = new ArrayList<>();
            if (!details.isEmpty()) {
                PredictionResultDetailDto firstDetail = details.get(0);
                if (firstDetail.getFeatureContributions() != null && !firstDetail.getFeatureContributions().isEmpty()) {
                    try {
                        Map<String, Double> contributions = objectMapper.readValue(firstDetail.getFeatureContributions(), new TypeReference<Map<String, Double>>() {});
                        featureContributionColumns.addAll(contributions.keySet());
                        Collections.sort(featureContributionColumns); // 按字母顺序排序以确保一致性
                    } catch (IOException e) {
                        log.warn("无法解析任务 {} 的第一个结果的特征贡献度JSON，将不导出贡献度列。", taskId, e);
                    }
                }
            }
            // --- 结束动态列生成 ---

            Row headerRow = sheet.createRow(0);
            // *** 修改: 更新表头列 ***
            List<String> staticColumns = Arrays.asList("样本ID", "鱼种名称", "分类标签", "置信度", "养殖概率", "野生概率", "排序");
            List<String> allColumns = new ArrayList<>(staticColumns);
            allColumns.addAll(featureContributionColumns);


            for (int i = 0; i < allColumns.size(); i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(allColumns.get(i));
                cell.setCellStyle(headerStyle);
            }

            int rowNum = 1;
            for (PredictionResultDetailDto detail : details) {
                Row row = sheet.createRow(rowNum++);

                // 样本ID (String)
                Cell cell0 = row.createCell(0);
                cell0.setCellValue(detail.getSampleId() != null ? detail.getSampleId() : "N/A");

                // 鱼种名称 (String)
                Cell cell1 = row.createCell(1);
                cell1.setCellValue(detail.getFishSpeciesName() != null ? detail.getFishSpeciesName() : "未知");

                // 分类结果 (String)
                Cell cell2 = row.createCell(2);
                cell2.setCellValue(detail.getClassificationLabel() != null ? detail.getClassificationLabel() : "N/A");

                // 置信度 (Double)
                Cell cell3 = row.createCell(3);
                if (detail.getConfidenceScore() != null) {
                   cell3.setCellValue(detail.getConfidenceScore());
                } else {
                   cell3.setBlank();
                }

                // *** 修改: 添加养殖概率列 ***
                Cell cell4 = row.createCell(4);
                if (detail.getFarmedProbability() != null) {
                    cell4.setCellValue(detail.getFarmedProbability()); // Export as number
                } else {
                   cell4.setBlank();
                }

                // *** 修改: 添加野生概率列 ***
                Cell cell5 = row.createCell(5);
                if (detail.getWildProbability() != null) {
                    cell5.setCellValue(detail.getWildProbability()); // Export as number
                } else {
                   cell5.setBlank();
                }

                // *** 修改: 排序移动到最后一列 ***
                Cell cell6 = row.createCell(6);
                if (detail.getRankOrder() != null) {
                   cell6.setCellValue(detail.getRankOrder());
                } else {
                   cell6.setBlank();
                }

                // --- 填充动态特征贡献度列 ---
                if (!featureContributionColumns.isEmpty()) {
                    Map<String, Double> contributionsMap = Collections.emptyMap();
                    if (detail.getFeatureContributions() != null && !detail.getFeatureContributions().isEmpty()) {
                        try {
                            contributionsMap = objectMapper.readValue(detail.getFeatureContributions(), new TypeReference<Map<String, Double>>() {});
                        } catch (IOException e) {
                            log.warn("无法解析任务 {} 中样本 {} 的特征贡献度JSON，贡献度单元格将留空。", taskId, detail.getSampleId());
                        }
                    }

                    for (int i = 0; i < featureContributionColumns.size(); i++) {
                        String featureName = featureContributionColumns.get(i);
                        Double value = contributionsMap.get(featureName);

                        Cell cell = row.createCell(staticColumns.size() + i);
                        if (value != null) {
                            cell.setCellValue(value);
                        } else {
                            cell.setBlank();
                        }
                    }
                }
            }

            for (int i = 0; i < allColumns.size(); i++) {
                sheet.autoSizeColumn(i);
            }

            workbook.write(outputStream);
            log.info("任务 ID {} 的 Excel 导出成功完成", taskId);
            return outputStream;

        } catch (IOException e) {
            log.error("导出任务 ID {} 的预测结果到Excel时出错: {}", taskId, e.getMessage(), e);
            throw new RuntimeException("导出预测结果失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("导出任务 ID {} 时发生意外错误: {}", taskId, e.getMessage(), e);
            throw new RuntimeException("导出时发生意外错误: " + e.getMessage());
        }
    }
}
