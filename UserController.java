cat > /var/www/api/fish-api/src/main/java/com/example/fishapi/controller/UserController.java << EOF
package com.example.fishapi.controller;

import com.example.fishapi.dto.ApiResponse; // 我们自己的 ApiResponse DTO
import com.example.fishapi.exception.ResourceNotFoundException;
import com.example.fishapi.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/users")
@Tag(name = "User Management", description = "APIs for managing user accounts")
@SecurityRequirement(name = "bearerAuth")
public class UserController {

    private static final Logger log = LoggerFactory.getLogger(UserController.class);

    private final UserService userService;

    @Autowired
    public UserController(UserService userService) {
        this.userService = userService;
    }

    // --- 删除用户端点 ---
    @DeleteMapping("/{userId}")
    @Operation(summary = "Delete a user account", description = "Allows a user to delete their own account or an admin to delete any user account.")
    // *** 使用全限定名引用 Swagger 的 ApiResponse ***
    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "User deleted successfully")
    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "403", description = "Forbidden - Insufficient permissions")
    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "User not found")
    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error during deletion")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<Object>> deleteUser(
            @Parameter(description = "ID of the user to delete", required = true) @PathVariable Long userId,
            Authentication authentication) {

        try {
            userService.deleteUser(userId, authentication);
            log.info("User account with ID: {} deleted successfully by user: {}", userId, authentication.getName());
            // 使用我们自己的 ApiResponse DTO 返回成功信息
            return ResponseEntity.ok(com.example.fishapi.dto.ApiResponse.success("User deleted successfully."));
        } catch (ResourceNotFoundException e) {
            log.warn("Attempt to delete non-existent user with ID: {}", userId);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                   .body(com.example.fishapi.dto.ApiResponse.error(e.getMessage()));
        } catch (SecurityException e) {
            log.warn("Unauthorized attempt to delete user with ID: {} by user: {}", userId, authentication.getName());
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                   .body(com.example.fishapi.dto.ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("Error deleting user with ID: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                   .body(com.example.fishapi.dto.ApiResponse.error("An error occurred while deleting the user account."));
        }
    }
} 
EOF