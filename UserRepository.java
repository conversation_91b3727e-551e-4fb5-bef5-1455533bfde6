cat > /var/www/api/fish-api/src/main/java/com/example/fishapi/repository/UserRepository.java << EOF
package com.example.fishapi.repository;

import com.example.fishapi.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    Optional<User> findByUsername(String username);
    Boolean existsByUsername(String username);
    Boolean existsByEmail(String email);

    /**
     * 根据邮箱查找用户
     * @param email 用户邮箱
     * @return Optional<User>
     */
    Optional<User> findByEmail(String email); 
}
EOF