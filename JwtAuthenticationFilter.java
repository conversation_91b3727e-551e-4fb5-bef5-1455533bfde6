# 注意：这是覆盖 JwtAuthenticationFilter.java 的最终版本
cat > /var/www/api/fish-api/src/main/java/com/example/fishapi/security/JwtAuthenticationFilter.java << EOF
package com.example.fishapi.security;

import com.example.fishapi.dto.LoginRequest;
import com.example.fishapi.security.CustomUserDetails;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

public class JwtAuthenticationFilter extends UsernamePasswordAuthenticationFilter {

    private static final Logger log = LoggerFactory.getLogger(JwtAuthenticationFilter.class);

    private final AuthenticationManager authenticationManager;
    private final String jwtSecret;
    private final long jwtExpirationMs;

    public JwtAuthenticationFilter(AuthenticationManager authenticationManager, String jwtSecret, long jwtExpirationMs) {
        this.authenticationManager = authenticationManager;
        this.jwtSecret = jwtSecret;
        this.jwtExpirationMs = jwtExpirationMs;
        this.setRequiresAuthenticationRequestMatcher(new AntPathRequestMatcher("/api/auth/login", "POST"));
        log.info("JwtAuthenticationFilter initialized to handle POST requests on /api/auth/login");
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException {
        try {
            LoginRequest creds = new ObjectMapper().readValue(request.getInputStream(), LoginRequest.class);
            log.debug("Attempting authentication for email: {}", creds.getEmail());
            return authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(creds.getEmail(), creds.getPassword())
            );
        } catch (IOException e) {
            log.error("Failed to parse authentication request body for login", e);
            throw new org.springframework.security.authentication.InternalAuthenticationServiceException("Failed to parse authentication request body", e);
        }
    }

    @Override
    protected void successfulAuthentication(HttpServletRequest request, HttpServletResponse response,
                                            FilterChain chain, Authentication authResult) throws IOException, ServletException {

        // *获取 CustomUserDetails 和 User 实体 
        CustomUserDetails customUserDetails = (CustomUserDetails) authResult.getPrincipal();
        com.example.fishapi.model.User userEntity = customUserDetails.getUser(); 

        String username = userEntity.getUsername(); //  username
        Long userId = userEntity.getId();         //  userId
        String email = userEntity.getEmail();       //  email
        log.info("Authentication successful for user: {}, userId: {}, email: {}", username, userId, email);

        // 获取权限信息 
        String authorities = customUserDetails.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.joining(","));

        byte[] signingKey = jwtSecret.getBytes();

        // 构建 JWT 
        String token = Jwts.builder()
                .setSubject(username)
                .claim("authorities", authorities)
                // 将 userId 放入 claim 
                .claim("userId", userId)
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + jwtExpirationMs))
                .signWith(Keys.hmacShaKeyFor(signingKey), SignatureAlgorithm.HS512)
                .compact();
        log.debug("Generated JWT for user: {}", username);

        // 在响应头中添加 Token
        response.addHeader("Authorization", "Bearer " + token);

        // 在响应体中包含 userId, username, email, token 
        Map<String, Object> responseBody = new HashMap<>();
        responseBody.put("token", token);
        responseBody.put("userId", userId);     
        responseBody.put("username", username); 
        responseBody.put("email", email);       
        // 不再返回权限列表
        // responseBody.put("authorities", customUserDetails.getAuthorities().stream()
        //                                     .map(GrantedAuthority::getAuthority)
        //                                     .collect(Collectors.toList()));

        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        response.getWriter().write(new ObjectMapper().writeValueAsString(responseBody));
        response.getWriter().flush();
    }

    @Override
    protected void unsuccessfulAuthentication(HttpServletRequest request, HttpServletResponse response,
                                                AuthenticationException failed) throws IOException, ServletException {
        log.warn("Authentication failed: {}", failed.getMessage());
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        Map<String, String> error = new HashMap<>();
        error.put("error", "Authentication Failed");
        error.put("message", "Invalid email or password");
        response.getWriter().write(new ObjectMapper().writeValueAsString(error));
        response.getWriter().flush();
    }
}
EOF