# 创建 ResetPasswordWithCodeRequest.java
cat << EOF > /var/www/api/fish-api/src/main/java/com/example/fishapi/dto/ResetPasswordWithCodeRequest.java
package com.example.fishapi.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;

public class ResetPasswordWithCodeRequest {

    @NotEmpty(message = "Email cannot be empty")
    @Email(message = "Invalid email format")
    private String email;

    @NotEmpty(message = "Verification code cannot be empty")
    @Size(min = 6, max = 6, message = "Verification code must be 6 digits")
    private String verificationCode;

    @NotEmpty(message = "New password cannot be empty")
    @Size(min = 6, message = "New password must be at least 6 characters long")
    private String newPassword;

    // Getters and Setters
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getVerificationCode() {
        return verificationCode;
    }

    public void setVerificationCode(String verificationCode) {
        this.verificationCode = verificationCode;
    }

    public String getNewPassword() {
        return newPassword;
    }

    public void setNewPassword(String newPassword) {
        this.newPassword = newPassword;
    }
}
EOF
echo "ResetPasswordWithCodeRequest.java created."