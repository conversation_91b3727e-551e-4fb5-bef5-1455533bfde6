admin@lavm-zam5kdzhoo:~$ cat /var/www/api/fish-api/src/main/java/com/example/fishapi/repository/PredictionTaskRepository.java
package com.example.fishapi.repository;

import com.example.fishapi.model.PredictionTask;
import com.example.fishapi.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface PredictionTaskRepository extends JpaRepository<PredictionTask, Long> {

    /**
     * 根据用户查找所有任务，按创建时间降序排序
     * @param user 用户实体
     * @return 任务列表
     */
    List<PredictionTask> findByUserOrderByCreatedAtDesc(User user);

    /**
     * 根据用户分页查找预测任务
     * @param user 用户实体
     * @param pageable 分页信息
     * @return 预测任务分页结果
     */
    Page<PredictionTask> findByUser(User user, Pageable pageable);

    /**
     * 根据任务ID和用户查找任务
     * @param id 任务ID
     * @param user 用户实体
     * @return 可选的任务实体
     */
    Optional<PredictionTask> findByIdAndUser(Long id, User user);

    // 移除 findByExternalTaskId 方法声明
    // Optional<PredictionTask> findByExternalTaskId(String externalTaskId);

    /**
     * 查找最新的10个任务
     * @return 最新的10个任务列表
     */
    List<PredictionTask> findTop10ByOrderByCreatedAtDesc();

    // 移除 findByExternalTaskIdIn 方法声明
    // List<PredictionTask> findByExternalTaskIdIn(List<String> externalTaskIds);
    
    /**
     * 删除用户的所有预测任务
     * @param user 用户实体
     * @return 被删除的记录数
     */
    int deleteByUser(User user);
    
    // 其他自定义查询方法...
}
