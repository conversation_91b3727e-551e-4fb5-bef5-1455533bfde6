sudo cat /var/www/api/fish-api/src/main/java/com/example/fishapi/model/FishSpecies.java
package com.example.fishapi.model;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@Entity
@Table(name = "fish_species")
public class FishSpecies {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "species_name", nullable = false, unique = true)
    private String speciesName;
    
    @Column(name = "name", insertable = false, updatable = false)
    private String name;

    @Column(name = "scientific_name")
    private String scientificName;

    @Column(name = "description", length = 1000)
    private String description;

    @Column(name = "is_wild", insertable = false, updatable = false)
    private Boolean isWild;

    @Column(name = "category", length = 50)
    private String category;
    
    @Column(name = "species_code", unique = true)
    private String speciesCode;
    
    @Column(name = "image_url")
    private String imageUrl;
    
    @Column(name = "origin_type", length = 20)
    private String originType;

    @OneToMany(mappedBy = "fishSpecies")
    @JsonManagedReference
    private List<PredictionResultDetail> resultDetails = new ArrayList<>();
    
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createdAt = now;
        this.updatedAt = now;
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}
admin@lavm-zam5kdzhoo:~$ 