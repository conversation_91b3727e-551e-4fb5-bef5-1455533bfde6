# 注意：这是覆盖 AuthController.java 的内容
cat > /var/www/api/fish-api/src/main/java/com/example/fishapi/controller/AuthController.java << EOF
package com.example.fishapi.controller;

// 现有导入保持
import com.example.fishapi.model.User;
import com.example.fishapi.repository.UserRepository;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

// Swagger 注解
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;

// 修改和新增的导入
import com.example.fishapi.service.UserService;
import com.example.fishapi.dto.ForgotPasswordRequest;
import com.example.fishapi.dto.ResetPasswordRequest;
import com.example.fishapi.dto.RegistrationRequest; // *** 导入新的 DTO ***
import com.example.fishapi.dto.SendCodeRequest;     // *** 导入新的 DTO ***
import com.example.fishapi.exception.ResourceNotFoundException;
import org.springframework.security.authentication.BadCredentialsException;

import java.util.HashMap;
import java.util.Map;

@RestController
@Tag(name = "Authentication & Registration", description = "APIs for user registration, login, and password management") // 更新 Tag 描述
@RequestMapping("/api/auth")
public class AuthController {
    private static final Logger log = LoggerFactory.getLogger(AuthController.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private UserService userService;

    // 发送注册验证码端点 
    @Operation(summary = "Send registration verification code", description = "Sends a verification code to the provided email for registration.")
    @ApiResponse(responseCode = "200", description = "Verification code sent (if email is available)", content = @Content(schema = @Schema(implementation = Map.class)))
    @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content(schema = @Schema(implementation = Map.class)))
    @PostMapping("/send-registration-code")
    public ResponseEntity<?> sendRegistrationCode(@Valid @RequestBody SendCodeRequest request) {
        try {
            userService.sendRegistrationVerificationCode(request);
            // 统一返回成功信息，隐藏邮箱是否已注册的状态
            return buildSuccessResponse("If the email address is available, a verification code has been sent.");
        } catch (Exception e) {
            log.error("Error sending registration code for {}: {}", request.getEmail(), e.getMessage(), e);
            return buildErrorResponse("An error occurred while processing your request.", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    // 注册端点，增加验证码校验 
    @Operation(summary = "Register a new user with verification code", description = "Creates a new user account after verifying the email code.")
    @ApiResponse(responseCode = "200", description = "User registered successfully", content = @Content(schema = @Schema(implementation = Map.class)))
    @ApiResponse(responseCode = "400", description = "Invalid input, username/email taken, or invalid/expired code", content = @Content(schema = @Schema(implementation = Map.class)))
    @PostMapping("/register")
    // 使用导入的 RegistrationRequest 
    public ResponseEntity<?> registerUser(@Valid @RequestBody RegistrationRequest registrationRequest) {
        // 1. 验证验证码
        if (!userService.verifyRegistrationCode(registrationRequest.getEmail(), registrationRequest.getCode())) {
            return buildErrorResponse("Invalid or expired verification code.", HttpStatus.BAD_REQUEST);
        }

        // 2. 检查用户名和邮箱是否可用
        if (userRepository.existsByUsername(registrationRequest.getUsername())) {
            return buildErrorResponse("Username is already taken!", HttpStatus.BAD_REQUEST);
        }
        if (userRepository.existsByEmail(registrationRequest.getEmail())) {
             log.warn("Email {} was verified but is now registered. Possible race condition?", registrationRequest.getEmail());
             return buildErrorResponse("Email is already in use!", HttpStatus.BAD_REQUEST);
        }

        // 3. 创建用户
        User user = new User();
        user.setUsername(registrationRequest.getUsername());
        user.setEmail(registrationRequest.getEmail());
        user.setPasswordHash(passwordEncoder.encode(registrationRequest.getPassword()));
        user.setUserType(User.UserType.USER);
        user.setStatus(User.UserStatus.ACTIVE);
        userRepository.save(user);

        log.info("User registered successfully after email verification: {}", user.getUsername());
        return buildSuccessResponse("User registered successfully!");
    }

    // 忘记密码/重置密码端点 (保持不变)
    @Operation(summary = "Initiate password reset", description = "Sends a verification code to the user's registered email.")
    @ApiResponse(responseCode = "200", description = "Password reset email sent (if email exists)", content = @Content(schema = @Schema(implementation = Map.class)))
    @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content(schema = @Schema(implementation = Map.class)))
    @PostMapping("/forgot-password")
    public ResponseEntity<?> forgotPassword(@Valid @RequestBody ForgotPasswordRequest request) {
        try {
            userService.initiatePasswordReset(request);
            log.info("Password reset initiated for email: {}", request.getEmail());
            return buildSuccessResponse("If the email address exists in our system, a password reset verification code has been sent.");
        } catch (ResourceNotFoundException | BadCredentialsException e) {
             log.warn("Forgot password attempt failed (user not found or inactive) for email: {}", request.getEmail());
             return buildSuccessResponse("If the email address exists in our system, a password reset verification code has been sent.");
        } catch (Exception e) {
            log.error("Error initiating password reset for {}: {}", request.getEmail(), e.getMessage(), e);
            return buildErrorResponse("An error occurred while processing your request.", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Reset password using verification code", description = "Sets a new password after verifying the code sent via email.")
    @ApiResponse(responseCode = "200", description = "Password has been reset successfully", content = @Content(schema = @Schema(implementation = Map.class)))
    @ApiResponse(responseCode = "400", description = "Invalid/expired code or invalid new password", content = @Content(schema = @Schema(implementation = Map.class)))
    @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content(schema = @Schema(implementation = Map.class)))
    @PostMapping("/reset-password")
    public ResponseEntity<?> resetPassword(@Valid @RequestBody ResetPasswordRequest request) {
        try {
            userService.resetPassword(request);
            log.info("Password successfully reset for email: {}", request.getEmail());
            return buildSuccessResponse("Password has been reset successfully.");
        } catch (ResourceNotFoundException e) {
             log.warn("Reset password attempt for potentially deleted user with email: {}", request.getEmail());
             return buildErrorResponse("Failed to reset password. Please try requesting a new code.", HttpStatus.BAD_REQUEST);
        } catch (BadCredentialsException | IllegalArgumentException e) {
            log.warn("Reset password attempt failed for email {}: {}", request.getEmail(), e.getMessage());
            return buildErrorResponse(e.getMessage(), HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error("Error resetting password for {}: {}", request.getEmail(), e.getMessage(), e);
            return buildErrorResponse("An error occurred while resetting the password.", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // Helper methods for response building (保持不变) 
    private ResponseEntity<Map<String, String>> buildSuccessResponse(String message) {
        Map<String, String> response = new HashMap<>();
        response.put("message", message);
        return ResponseEntity.ok(response);
    }

    private ResponseEntity<Map<String, String>> buildErrorResponse(String message, HttpStatus status) {
        Map<String, String> response = new HashMap<>();
        response.put("error", message);
        return ResponseEntity.status(status).body(response);
    }
}
EOF