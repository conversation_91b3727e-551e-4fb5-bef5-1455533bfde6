# 创建 EmailVerificationService.java 接口
cat << EOF > /var/www/api/fish-api/src/main/java/com/example/fishapi/service/EmailVerificationService.java
package com.example.fishapi.service;

public interface EmailVerificationService {

    /**
     * 生成验证码，保存并发送邮件
     * @param email 目标邮箱
     */
    void generateAndSendVerificationCode(String email);

    /**
     * 验证邮箱和验证码是否匹配且未过期
     * @param email 邮箱
     * @param code  验证码
     * @return true 如果验证通过，否则 false
     */
    boolean verifyCode(String email, String code);
}
EOF
echo "EmailVerificationService.java interface created."
