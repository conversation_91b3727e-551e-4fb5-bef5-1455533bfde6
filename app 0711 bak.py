import os
import logging
import joblib
import pandas as pd
from flask import Flask, request, jsonify
from flask_cors import CORS
import numpy as np
from werkzeug.utils import secure_filename
import tempfile
import json
import importlib # Needed for checking xlrd
import time

# --- 配置日志 ---
log_dir = os.getenv("API_LOG_DIR", "/var/log/fish-api-python") # 从环境变量或默认路径获取日志目录
os.makedirs(log_dir, exist_ok=True) # 确保日志目录存在
log_file_path = os.path.join(log_dir, "api_server.log")

logging.basicConfig(level=logging.DEBUG, # 设置为 DEBUG 级别可以看到更详细的信息
                    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                    handlers=[
                        logging.FileHandler(log_file_path),
                        logging.StreamHandler() # 同时输出到控制台
                    ])
logger = logging.getLogger("api_server")

logger.info("API服务器启动")

# --- 常量和配置 ---
# 默认特征列表 (确保与训练时一致)
DEFAULT_FEATURES = {
    "lianyu": [
        "p__Bacteroidota", "c__Geothermincolia", "o__Pseudomonadales",
        "f__Pseudomonadaceae", "g__Anoxymicrobium", "s__Acinetobacter johnsonii"
    ],
    "yongyu": [
        "p__Fusobacteriota", "c__Actinomycetia", "o__UBA2241",
        "f__Pseudomonadaceae", "g__unclassified_Mycobacteriales", "s__Methylosinus sp000379125"
    ]
}
SUPPORTED_FISH_TYPES = list(DEFAULT_FEATURES.keys())

# 确定模型目录
script_dir = os.path.dirname(os.path.abspath(__file__))
logger.info(f"当前工作目录: {os.getcwd()}")
logger.info(f"脚本所在目录: {script_dir}")
model_path_env = os.getenv("MODEL_PATH")
logger.info(f"模型目录环境变量(MODEL_PATH): {model_path_env}")
MODEL_DIR = model_path_env if model_path_env else os.path.join(script_dir, "models")
logger.info(f"最终使用的模型目录 (MODEL_DIR): {MODEL_DIR}")
logger.info(f"支持的鱼类型: {SUPPORTED_FISH_TYPES}")
logger.info(f"使用的特征列表: {json.dumps(DEFAULT_FEATURES, indent=2, ensure_ascii=False)}")

# --- 模型管理 ---
class ModelManager:
    def __init__(self, model_dir, supported_types):
        logger.info(f"初始化ModelManager，模型目录: {model_dir}, 支持鱼种: {supported_types}")
        self.model_dir = model_dir
        self.supported_types = supported_types
        self.models = {}
        self.scalers = {}
        self.encoders = {}
        self.load_components()

    def load_components(self):
        logger.info("开始加载所有支持的模型组件")
        loaded_count = 0
        for fish_type in self.supported_types:
            logger.info(f"开始加载 {fish_type} 的模型组件")
            try:
                # --- 路径设置：在各自的子目录中查找文件 ---
                fish_model_dir = os.path.join(self.model_dir, fish_type) # 定义鱼种的子目录
                model_path = os.path.join(fish_model_dir, f"{fish_type}_model.joblib")
                encoder_path = os.path.join(fish_model_dir, f"{fish_type}_encoder.joblib")
                scaler_path = os.path.join(fish_model_dir, f"{fish_type}_scaler.joblib")
                # --- 结束路径设置 ---

                logger.debug(f"鱼种 {fish_type} 路径: 模型={model_path}, 编码器={encoder_path}, 标准化器={scaler_path}")

                if not os.path.exists(model_path):
                     logger.error(f"模型文件不存在: {model_path}")
                     raise FileNotFoundError(f"模型文件未找到: {model_path}")
                if not os.path.exists(encoder_path):
                    logger.error(f"编码器文件不存在: {encoder_path}")
                    raise FileNotFoundError(f"编码器文件未找到: {encoder_path}")
                if not os.path.exists(scaler_path):
                    logger.error(f"标准化器文件不存在: {scaler_path}")
                    raise FileNotFoundError(f"标准化器文件未找到: {scaler_path}")

                self.models[fish_type] = joblib.load(model_path)
                logger.info(f"成功加载模型: {model_path}")
                self.encoders[fish_type] = joblib.load(encoder_path)
                logger.info(f"成功加载编码器: {encoder_path}")
                # 打印编码器类别以供调试
                try:
                    logger.info(f"编码器 {encoder_path} 的类别: {self.encoders[fish_type].classes_}")
                except AttributeError:
                    logger.warning(f"编码器 {encoder_path} 没有 classes_ 属性")

                self.scalers[fish_type] = joblib.load(scaler_path)
                logger.info(f"成功加载标准化器: {scaler_path}")
                loaded_count += 1
            except FileNotFoundError as fnf_err:
                logger.error(f"加载 {fish_type} 组件时文件未找到: {fnf_err}")
                logger.warning(f"加载 {fish_type} 组件失败")
            except Exception as e:
                logger.error(f"加载 {fish_type} 模型组件时发生意外错误: {e}", exc_info=True)
                logger.warning(f"加载 {fish_type} 组件失败")
        logger.info(f"模型组件加载完成: {loaded_count}/{len(self.supported_types)} 成功")
        if loaded_count < len(self.supported_types): # 如果不是所有模型都加载成功，则记录警告
             logger.warning(f"并非所有鱼种的模型组件都已成功加载。已加载: {list(self.models.keys())}")
        if loaded_count == 0:
             logger.error("未能成功加载任何支持的模型组件。API 初始化失败。")


    def get_predictor(self, fish_type):
        if fish_type not in self.supported_types:
            logger.warning(f"请求不支持的鱼种: {fish_type}")
            return None
        # 检查该鱼种的所有组件是否都已加载
        if fish_type not in self.models or fish_type not in self.scalers or fish_type not in self.encoders:
            logger.warning(f"鱼种 {fish_type} 的模型组件未完全加载，无法创建预测器")
            return None
        return SimpleModelPredictor(
            self.models[fish_type],
            self.scalers[fish_type],
            self.encoders[fish_type],
            DEFAULT_FEATURES.get(fish_type, []), # 获取对应鱼种的特征列表
            fish_type
        )

class SimpleModelPredictor:
    def __init__(self, model, scaler, encoder, features, fish_type):
        self.model = model
        self.scaler = scaler
        self.encoder = encoder
        self.features = features # 使用传入的特征列表
        self.fish_type = fish_type
        logger.info(f"预测器已初始化，鱼种: {fish_type}, 使用特征: {features}")

    def preprocess_data(self, df):
        """预处理数据，包括特征选择、填充NaN和标准化"""
        logger.debug(f"开始预处理数据，鱼种: {self.fish_type}, 原始列: {df.columns.tolist()}") # 记录原始列
        # 1. 特征选择 (仅保留 DEFAULT_FEATURES 中定义的列)
        missing_features = [f for f in self.features if f not in df.columns]
        if missing_features:
            logger.warning(f"鱼种 {self.fish_type}: 输入数据缺少以下必要特征: {missing_features}。将使用 0 填充。")
            for feature in missing_features:
                df[feature] = 0 # 为缺失的特征列填充 0
        try:
            # 确保列的顺序与训练时一致
            df_selected = df[self.features]
        except KeyError as ke:
            logger.error(f"特征选择时发生KeyError: {ke}. 检查 DEFAULT_FEATURES 是否与实际填充后的列匹配。实际列: {df.columns.tolist()}", exc_info=True)
            raise ValueError(f"Feature selection failed for {self.fish_type}. Missing key: {ke}")

        logger.debug(f"特征选择后 ({len(self.features)} 个特征): {df_selected.columns.tolist()}")

        # 2. 处理 NaN 值 (使用 0 填充)
        nan_counts = df_selected.isnull().sum()
        if nan_counts.sum() > 0:
             logger.warning(f"鱼种 {self.fish_type}: 在选定特征中发现 {nan_counts.sum()} 个 NaN 值，将使用 0 填充。列详情: {nan_counts[nan_counts > 0].to_dict()}")
             df_filled = df_selected.fillna(0)
        else:
             logger.debug(f"鱼种 {self.fish_type}: 选定特征中无 NaN 值。")
             df_filled = df_selected

        # 3. 数据标准化
        try:
            scaled_data = self.scaler.transform(df_filled)
            logger.debug(f"数据标准化完成，鱼种: {self.fish_type}，标准化后数据维度: {scaled_data.shape}")
            return scaled_data, df_filled.index # 返回标准化数据和原始索引
        except Exception as e:
            logger.error(f"数据标准化时出错，鱼种: {self.fish_type}: {e}", exc_info=True)
            raise

    def predict(self, df):
        """执行预测"""
        try:
            logger.info(f"开始对鱼种 {self.fish_type} 进行预测...")
            scaled_data, original_index = self.preprocess_data(df.copy()) # 传入副本以防修改原始数据
            logger.debug(f"模型输入数据维度: {scaled_data.shape}")
            predictions_encoded = self.model.predict(scaled_data)
            logger.debug(f"模型原始预测（编码）: {predictions_encoded.tolist()[:10]}...") # 日志中显示前10个原始预测

            if self.encoder is None:
                logger.error(f"鱼种 {self.fish_type} 的编码器未加载，无法解码预测结果！")
                raise ValueError(f"Encoder for {self.fish_type} is not loaded.")

            predictions_decoded = self.encoder.inverse_transform(predictions_encoded)
            logger.info(f"鱼种 {self.fish_type} 预测完成并解码，共 {len(predictions_decoded)} 条结果。")
            logger.debug(f"解码后的预测结果示例: {predictions_decoded.tolist()[:10]}...")

            # 直接使用解码后的中文标签创建 DataFrame
            results_df = pd.DataFrame({"prediction": predictions_decoded}, index=original_index)

            # *** 添加置信度和特定类别的概率 ***
            if hasattr(self.model, "predict_proba"):
                 try:
                     probabilities = self.model.predict_proba(scaled_data)
                     # 获取每个样本预测类别的置信度 (最高概率)
                     confidence_scores = np.max(probabilities, axis=1)
                     results_df['confidence_score'] = confidence_scores
                     logger.debug(f"已添加置信度分数。示例: {confidence_scores.tolist()[:10]}...")

                     # --- 获取野生和养殖概率 ---
                     wild_index = -1
                     farmed_index = -1
                     if hasattr(self.encoder, 'classes_'):
                         class_labels = self.encoder.classes_
                         logger.debug(f"Encoder classes for {self.fish_type}: {class_labels}")
                         for i, label in enumerate(class_labels):
                             if isinstance(label, str): # Ensure it's a string
                                 if label.startswith("野生") and wild_index == -1:
                                     wild_index = i
                                 elif label.startswith("养殖") and farmed_index == -1:
                                     farmed_index = i
                             else:
                                 logger.warning(f"Non-string label found in encoder classes: {label} at index {i}")

                         if wild_index != -1:
                             results_df['wild_probability'] = probabilities[:, wild_index]
                             logger.debug(f"添加 '野生' 概率 (列索引 {wild_index}). 示例: {results_df['wild_probability'].tolist()[:10]}...")
                         else:
                             logger.warning(f"未在编码器类别中找到 '野生' 前缀标签 ({self.fish_type})。'wild_probability' 将为 None。")
                             results_df['wild_probability'] = None

                         if farmed_index != -1:
                             results_df['farmed_probability'] = probabilities[:, farmed_index]
                             logger.debug(f"添加 '养殖' 概率 (列索引 {farmed_index}). 示例: {results_df['farmed_probability'].tolist()[:10]}...")
                         else:
                             logger.warning(f"未在编码器类别中找到 '养殖' 前缀标签 ({self.fish_type})。'farmed_probability' 将为 None。")
                             results_df['farmed_probability'] = None
                     else:
                         logger.warning(f"编码器 ({self.fish_type}) 没有 'classes_' 属性，无法确定野生/养殖概率索引。")
                         results_df['wild_probability'] = None
                         results_df['farmed_probability'] = None
                     # --- 结束获取概率 ---

                 except Exception as proba_err:
                     logger.warning(f"获取概率时出错，鱼种 {self.fish_type}: {proba_err}", exc_info=False)
                     results_df['confidence_score'] = None
                     results_df['wild_probability'] = None
                     results_df['farmed_probability'] = None
            else:
                 logger.info(f"模型 {type(self.model).__name__} 不支持 predict_proba，无法获取概率分数。")
                 results_df['confidence_score'] = None
                 results_df['wild_probability'] = None
                 results_df['farmed_probability'] = None
            # *** 结束添加概率 ***

            return results_df

        except Exception as e:
            logger.error(f"预测过程中发生错误，鱼种: {self.fish_type}: {e}", exc_info=True)
            raise

# --- Flask 应用 ---
app = Flask(__name__)
CORS(app) # 允许跨域请求

# --- 全局模型管理器 ---
logger.info("初始化全局模型管理器...")
model_manager = ModelManager(MODEL_DIR, SUPPORTED_FISH_TYPES)

# --- 健康检查路由 ---
@app.route("/", methods=["GET"])
def health_check():
    logger.info("收到健康检查请求")
    # 检查是否所有支持的类型都有加载的模型
    loaded_models_count = len(model_manager.models)
    supported_types_count = len(SUPPORTED_FISH_TYPES)
    if loaded_models_count == supported_types_count:
         return jsonify({"status": "healthy", "message": f"API is running and all {supported_types_count} models are loaded."}), 200
    elif loaded_models_count > 0:
         logger.warning(f"健康检查警告：并非所有模型都已加载。已加载: {list(model_manager.models.keys())}")
         return jsonify({"status": "partially_healthy", "message": f"API is running but only {loaded_models_count}/{supported_types_count} models are loaded."}), 200 # 返回 200 但状态不同
    else:
        logger.error("健康检查失败：模型未加载")
        return jsonify({"status": "unhealthy", "message": "API is running but failed to load any models."}), 500

# --- 预测路由 ---
@app.route("/predict", methods=["POST"])
def predict_route():
    request_start_time = time.time() # 记录请求开始时间
    logger.info("收到预测请求")
    predictor = None
    temp_file_path = None
    df_original = None # 用于存储原始特征

    try:
        fish_type = request.form.get("fish_type")
        logger.debug(f"请求中的鱼种: {fish_type}")

        if not fish_type:
            logger.warning("请求中缺少 fish_type 参数")
            return jsonify({"error": "Missing fish_type parameter"}), 400

        if fish_type not in SUPPORTED_FISH_TYPES:
            logger.warning(f"请求了不支持的鱼种: {fish_type}")
            return jsonify({"error": f"Unsupported fish_type: {fish_type}. Supported types: {SUPPORTED_FISH_TYPES}"}), 400

        predictor = model_manager.get_predictor(fish_type)
        if predictor is None:
             # 如果特定鱼种的模型未加载，尝试重新加载所有模型
             logger.error(f"无法获取鱼种 {fish_type} 的预测器。尝试重新加载所有组件...")
             model_manager.load_components() # 尝试重新加载
             predictor = model_manager.get_predictor(fish_type) # 再次获取
             if predictor is None:
                 logger.error(f"重新加载后仍无法获取 {fish_type} 的预测器。请检查模型文件是否存在于子目录 {fish_type} 中。")
                 return jsonify({"error": f"Predictor for {fish_type} could not be initialized. Check server logs for missing files in {fish_type} subdirectory."}), 500
             else:
                 logger.info(f"重新加载后成功获取 {fish_type} 的预测器。")

        # --- 文件处理 ---
        df = None # 初始化 df
        if "file" in request.files:
            logger.info("请求类型: 文件上传")
            file = request.files["file"]
            if file.filename == "":
                logger.warning("收到空文件名")
                return jsonify({"error": "No selected file"}), 400

            filename = secure_filename(file.filename)
            # 使用临时文件处理上传，更安全
            with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(filename)[1]) as temp_file:
                file.save(temp_file.name)
                temp_file_path = temp_file.name # 保存临时文件路径以备后用
                logger.info(f"上传文件已临时保存到: {temp_file_path}")

            file_ext = os.path.splitext(filename)[1].lower()
            logger.info(f"接收到文件预测请求: 鱼种={fish_type}, 临时文件={temp_file_path}, 扩展名={file_ext}")

            try:
                if file_ext == ".xlsx" or file_ext == ".xls":
                    excel_engine = "openpyxl" if file_ext == ".xlsx" else None # xlrd 不再默认支持 xlsx
                    if file_ext == ".xls" and excel_engine is None:
                         try:
                             importlib.import_module("xlrd") # 检查是否安装了 xlrd
                             excel_engine = "xlrd"
                         except ImportError:
                              logger.error("需要 xlrd 库来读取 .xls 文件，但未安装。请运行 pip install xlrd")
                              return jsonify({"error": "Missing required library 'xlrd' to read .xls files."}), 500

                    logger.info(f"开始读取Excel文件: {temp_file_path}，使用引擎: {excel_engine}")
                    df = pd.read_excel(temp_file_path, index_col=0, engine=excel_engine)
                    logger.info(f"成功读取Excel文件，维度: {df.shape}，原始列名: {df.columns.tolist()}")
                elif file_ext == ".csv":
                    logger.info(f"开始读取CSV文件: {temp_file_path}")
                    df = pd.read_csv(temp_file_path, index_col=0)
                    logger.info(f"成功读取CSV文件，维度: {df.shape}，原始列名: {df.columns.tolist()}")
                else:
                    logger.warning(f"不支持的文件类型: {file_ext}")
                    return jsonify({"error": f"Unsupported file type: {file_ext}. Please upload .xlsx, .xls, or .csv"}), 400
            except Exception as read_err:
                 logger.error(f"读取临时文件 {temp_file_path} 时出错: {read_err}", exc_info=True)
                 return jsonify({"error": f"Error reading file: {read_err}"}), 500
            finally:
                 # 读取完成后立即删除临时文件
                 if temp_file_path and os.path.exists(temp_file_path):
                     try:
                         os.remove(temp_file_path)
                         logger.debug(f"已删除临时文件: {temp_file_path}")
                     except Exception as rm_err:
                         logger.error(f"删除临时文件 {temp_file_path} 时出错: {rm_err}")
                     temp_file_path = None # 清空路径变量


        elif "file_path" in request.form:
             logger.info("请求类型: 文件路径")
             file_path = request.form.get("file_path")
             logger.info(f"接收到文件路径预测请求: 鱼种={fish_type}, 路径={file_path}")
             if not file_path or not os.path.exists(file_path):
                logger.error(f"提供的文件路径无效或文件不存在: {file_path}")
                return jsonify({"error": f"Invalid or non-existent file path: {file_path}"}), 400

             file_ext = os.path.splitext(file_path)[1].lower()
             try:
                if file_ext == ".xlsx" or file_ext == ".xls":
                    excel_engine = "openpyxl" if file_ext == ".xlsx" else None
                    if file_ext == ".xls" and excel_engine is None:
                         try:
                             importlib.import_module("xlrd")
                             excel_engine = "xlrd"
                         except ImportError:
                              logger.error("需要 xlrd 库来读取 .xls 文件，但未安装。请运行 pip install xlrd")
                              return jsonify({"error": "Missing required library 'xlrd' to read .xls files."}), 500

                    logger.info(f"开始读取Excel文件路径: {file_path}，使用引擎: {excel_engine}")
                    df = pd.read_excel(file_path, index_col=0, engine=excel_engine)
                    logger.info(f"成功读取Excel文件路径，维度: {df.shape}，原始列名: {df.columns.tolist()}")
                elif file_ext == ".csv":
                    logger.info(f"开始读取CSV文件路径: {file_path}")
                    df = pd.read_csv(file_path, index_col=0)
                    logger.info(f"成功读取CSV文件路径，维度: {df.shape}，原始列名: {df.columns.tolist()}")
                else:
                    logger.warning(f"路径指向不支持的文件类型: {file_ext}")
                    return jsonify({"error": f"File path points to unsupported type: {file_ext}. Supported: .xlsx, .xls, .csv"}), 400
             except Exception as read_err:
                 logger.error(f"读取文件路径 {file_path} 时出错: {read_err}", exc_info=True)
                 return jsonify({"error": f"Error reading file from path: {read_err}"}), 500
        else:
            logger.warning("请求中既没有文件上传也没有文件路径")
            return jsonify({"error": "Request must contain either file upload or file_path parameter"}), 400

        # *** 保存原始 DataFrame 用于提取特征 ***
        df_original = df.copy() if df is not None else None

        # --- 执行预测 ---
        predict_start_time = time.time() # 记录预测开始时间
        results_df = predictor.predict(df) # results_df 现在包含 'prediction', 'confidence_score', 'wild_probability', 'farmed_probability'
        predict_duration = time.time() - predict_start_time # 计算预测耗时
        logger.info(f"模型预测耗时: {predict_duration:.4f} 秒")

        # --- 构建响应 ---
        # 将索引（SampleID）重置为普通列，以便包含在JSON中
        results_df_reset = results_df.reset_index()
        # 重命名索引列为 'SampleID' (如果它没有名字或者名字是 'index')
        if 'index' in results_df_reset.columns:
             results_df_reset = results_df_reset.rename(columns={'index': 'SampleID'})
        # 检查第一列是否是原始索引名（如果不是 'index'）
        elif df_original is not None and df_original.index.name and results_df_reset.columns[0] == df_original.index.name and df_original.index.name != 'SampleID':
             logger.warning(f"索引列名称不是 'SampleID'，而是 '{results_df_reset.columns[0]}'. 将尝试重命名。")
             results_df_reset = results_df_reset.rename(columns={results_df_reset.columns[0]: 'SampleID'})
        elif results_df_reset.columns[0] != 'SampleID':
             logger.warning(f"索引列名称既不是 'index' 也不是原始索引名 '{df_original.index.name if df_original is not None else ''}'，而是 '{results_df_reset.columns[0]}'. 将强制命名为 'SampleID'。")
             results_df_reset.rename(columns={results_df_reset.columns[0]: 'SampleID'}, inplace=True)


        predictions_list = results_df_reset.to_dict(orient="records")

        # --- 添加 sampleFeatures 到 predictions_list ---
        if df_original is not None:
            original_features_dict = df_original.to_dict(orient='index')
            for pred_dict in predictions_list:
                sample_id = pred_dict.get('SampleID')
                if sample_id in original_features_dict:
                    feature_dict = original_features_dict[sample_id]
                    try:
                        # Ensure all values are serializable (handle numpy types)
                        serializable_features = {k: (v.item() if isinstance(v, np.generic) else v) for k, v in feature_dict.items()}
                        # Convert to JSON string for Java
                        pred_dict['sampleFeatures'] = json.dumps(serializable_features, ensure_ascii=False, allow_nan=False) # ensure not NaN
                    except TypeError as e:
                        logger.error(f"序列化特征时出错 SampleID {sample_id}: {e}")
                        pred_dict['sampleFeatures'] = json.dumps({"error": "特征序列化失败"}, ensure_ascii=False)
                    except ValueError as e: # Catch NaN/Infinity errors from json.dumps
                        logger.error(f"序列化特征时包含非法值 (NaN/Inf?) SampleID {sample_id}: {e}")
                        # Attempt to clean and serialize again, or provide error JSON
                        cleaned_features = {}
                        for k, v in feature_dict.items():
                            if isinstance(v, (float, np.floating)) and (np.isnan(v) or np.isinf(v)):
                                cleaned_features[k] = str(v) # Convert NaN/Inf to string
                            elif isinstance(v, np.generic):
                                cleaned_features[k] = v.item()
                            else:
                                cleaned_features[k] = v
                        try:
                            pred_dict['sampleFeatures'] = json.dumps(cleaned_features, ensure_ascii=False)
                        except Exception as final_e:
                             logger.error(f"清理后序列化特征仍然失败 SampleID {sample_id}: {final_e}")
                             pred_dict['sampleFeatures'] = json.dumps({"error": "特征序列化最终失败"}, ensure_ascii=False)

                else:
                    logger.warning(f"无法找到 SampleID 的原始特征: {sample_id}")
                    pred_dict['sampleFeatures'] = None # Or an empty JSON object '{}'
        else:
             logger.warning("原始 DataFrame (df_original) 为 None，无法添加 sampleFeatures。")
             for pred_dict in predictions_list:
                 pred_dict['sampleFeatures'] = None
        # --- 结束添加 sampleFeatures ---


        # --- 修正统计逻辑：基于中文标签前缀 ---
        farmed_count = int(results_df["prediction"].astype(str).str.startswith("养殖").sum())
        wild_count = int(results_df["prediction"].astype(str).str.startswith("野生").sum())
        total_samples = len(results_df)
        logger.info(f"预测统计: 总样本={total_samples}, farmed={farmed_count}, wild={wild_count}")

        # 获取平均置信度
        avg_confidence = None
        if 'confidence_score' in results_df.columns and results_df['confidence_score'].notna().any():
            avg_confidence = results_df['confidence_score'].mean()
            logger.info(f"平均置信度: {avg_confidence:.4f}")

        # --- 计算总处理时间 ---
        request_end_time = time.time()
        total_duration = request_end_time - request_start_time
        logger.info(f"请求总处理耗时: {total_duration:.4f} 秒")

        # --- 构建最终响应 ---
        response_data = {
            # predictions_list 现在包含 sampleFeatures, wild_probability, farmed_probability
            "predictions": predictions_list,
            "summary": {
                "fish_type": fish_type,
                "total_samples": total_samples,
                "farmed_count": farmed_count,
                "wild_count": wild_count,
                "farmed_ratio": farmed_count / total_samples if total_samples > 0 else 0,
                "wild_ratio": wild_count / total_samples if total_samples > 0 else 0,
                "average_confidence": avg_confidence
            },
            "analysis_time_seconds": total_duration
        }

        # --- 打印调试日志 ---
        logger.debug(f"DEBUG: Returning JSON: {response_data}") # 使用 logger 打印

        logger.info("预测请求处理完成")
        return jsonify(response_data), 200

    except Exception as e:
        logger.error(f"处理预测请求时发生未捕获的错误: {e}", exc_info=True)
        # 即使出错，也尝试计算已用时间
        request_end_time = time.time()
        total_duration = request_end_time - request_start_time
        logger.info(f"请求处理出错，已用时: {total_duration:.4f} 秒")
        return jsonify({"error": "An internal server error occurred. Please check the logs.", "details": str(e)}), 500
    finally:
         # 确保即使发生错误也尝试删除临时文件
         if temp_file_path and os.path.exists(temp_file_path):
             try:
                 os.remove(temp_file_path)
                 logger.info(f"成功删除临时文件: {temp_file_path}")
             except Exception as rm_err:
                 logger.error(f"删除临时文件 {temp_file_path} 时出错: {rm_err}")


if __name__ == "__main__":
    logger.info("以开发模式启动 Flask 服务器")
    # 直接运行时监听 0.0.0.0 允许外部访问，端口 5000
    app.run(host="0.0.0.0", port=5000, debug=False)