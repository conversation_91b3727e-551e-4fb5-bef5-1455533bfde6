package com.example.fishapi.model;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
// Removed lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import java.util.Objects;

@Entity
@Table(name = "prediction_summary",
       uniqueConstraints = {
           @UniqueConstraint(columnNames = {"prediction_result_id"}),
           @UniqueConstraint(columnNames = {"prediction_task_id"})
       })
// Removed @Data
@NoArgsConstructor
@AllArgsConstructor
public class PredictionSummary {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // 与 PredictionResult 的一对一关联
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "prediction_result_id",
                referencedColumnName = "id",
                unique = true,
                nullable = true) // Allow null if result hasn't been created yet
    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    private PredictionResult predictionResult;

    // 与 PredictionTask 的一对一关联
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "prediction_task_id",
                referencedColumnName = "id",
                unique = true) // Assuming task ID must be unique for summary
    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    private PredictionTask predictionTask;

    // 聚合结果字段
    @Column(name = "top_classification_label", length = 255)
    private String topClassificationLabel; // "野生" 或 "养殖" 或 "混合" 或顶级物种名

    @Column(name = "top_classification_confidence", precision = 19, scale = 9)
    private BigDecimal topClassificationConfidence; // 顶层 JSON 中的置信度

    @Column(name = "total_species_count")
    private Integer totalSpeciesCount; // 总识别物种数

    @Column(name = "wild_count")
    private Integer wildCount; // 野生鱼种计数

    @Column(name = "farmed_count")
    private Integer farmedCount; // 养殖鱼种计数

    @Column(name = "wild_percentage", precision = 5, scale = 2)
    private BigDecimal wildPercentage; // 野生百分比 (0-100.00)

    @Column(name = "farmed_percentage", precision = 5, scale = 2)
    private BigDecimal farmedPercentage; // 养殖百分比 (0-100.00)

    @Column(name = "analysis_time_seconds")
    private Double analysisTimeSeconds; // 分析耗时(秒)

    // 时间戳
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = true)
    private LocalDateTime updatedAt;

    // --- Manually added Getters and Setters ---

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public PredictionResult getPredictionResult() {
        return predictionResult;
    }

    public void setPredictionResult(PredictionResult predictionResult) {
        this.predictionResult = predictionResult;
    }

    public PredictionTask getPredictionTask() {
        return predictionTask;
    }

    public void setPredictionTask(PredictionTask predictionTask) {
        this.predictionTask = predictionTask;
    }

    public String getTopClassificationLabel() {
        return topClassificationLabel;
    }

    public void setTopClassificationLabel(String topClassificationLabel) {
        this.topClassificationLabel = topClassificationLabel;
    }

    public BigDecimal getTopClassificationConfidence() {
        return topClassificationConfidence;
    }

    public void setTopClassificationConfidence(BigDecimal topClassificationConfidence) {
        this.topClassificationConfidence = topClassificationConfidence;
    }

    public Integer getTotalSpeciesCount() {
        return totalSpeciesCount;
    }

    public void setTotalSpeciesCount(Integer totalSpeciesCount) {
        this.totalSpeciesCount = totalSpeciesCount;
    }

    public Integer getWildCount() {
        return wildCount;
    }

    public void setWildCount(Integer wildCount) {
        this.wildCount = wildCount;
    }

    public Integer getFarmedCount() {
        return farmedCount;
    }

    public void setFarmedCount(Integer farmedCount) {
        this.farmedCount = farmedCount;
    }

    public BigDecimal getWildPercentage() {
        return wildPercentage;
    }

    public void setWildPercentage(BigDecimal wildPercentage) {
        this.wildPercentage = wildPercentage;
    }

    public BigDecimal getFarmedPercentage() {
        return farmedPercentage;
    }

    public void setFarmedPercentage(BigDecimal farmedPercentage) {
        this.farmedPercentage = farmedPercentage;
    }

    public Double getAnalysisTimeSeconds() {
        return analysisTimeSeconds;
    }

    public void setAnalysisTimeSeconds(Double analysisTimeSeconds) {
        this.analysisTimeSeconds = analysisTimeSeconds;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    // --- equals() and hashCode() - generated based on id ---

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PredictionSummary that = (PredictionSummary) o;
        return id != null ? id.equals(that.id) : super.equals(o);
    }

    @Override
    public int hashCode() {
        return id != null ? Objects.hash(id) : super.hashCode();
    }

    // --- toString() - Corrected: Removed problematic single quotes ---
    @Override
    public String toString() {
        return "PredictionSummary{" +
               "id=" + id +
               ", predictionResultId=" + (predictionResult != null ? predictionResult.getId() : "null") + // Added null check
               ", predictionTaskId=" + (predictionTask != null ? predictionTask.getId() : "null") + // Added null check
               ", topClassificationLabel=" + topClassificationLabel + // Removed quotes
               ", wildCount=" + wildCount +
               ", farmedCount=" + farmedCount +
               ", createdAt=" + createdAt +
               '}';
    }
}