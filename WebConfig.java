/var/www/api/fish-api/src/main/java/com/example/fishapi/WebConfig.java
package com.example.fishapi.config; 

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/api/**") // 对 /api/ 下的所有路径生效
            .allowedOrigins("http://localhost:5173", "https://www.cjfish.com")) //  Vue 开发服务器
            .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS") // 允许 HTTP 方法
            .allowedHeaders("*") // 允许所有请求头
            .allowCredentials(true); // 发送 Cookie 或基于 Session 的认证
    }
}