# 创建 EmailVerificationCode.java
cat << EOF > /var/www/api/fish-api/src/main/java/com/example/fishapi/model/EmailVerificationCode.java
package com.example.fishapi.model;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime; // 使用 Java 8 时间 API

@Entity
@Table(name = "email_verification_codes", indexes = {
    @Index(name = "idx_email_verification_email", columnList = "email") // 为 email 添加索引，加快查找
})
@Data
@NoArgsConstructor
public class EmailVerificationCode {

    private static final int EXPIRATION_MINUTES = 10; // 验证码有效期（例如：10分钟）

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String email;

    @Column(nullable = false)
    private String code; // 存储验证码

    @Column(nullable = false)
    private LocalDateTime expiryTime; // 存储过期时间

    public EmailVerificationCode(String email, String code) {
        this.email = email;
        this.code = code;
        this.expiryTime = LocalDateTime.now().plusMinutes(EXPIRATION_MINUTES);
    }

    public boolean isExpired() {
        return LocalDateTime.now().isAfter(this.expiryTime);
    }
}
EOF
echo "EmailVerificationCode.java created."