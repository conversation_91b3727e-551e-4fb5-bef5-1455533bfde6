# 创建 SendCodeRequest.java
cat > /var/www/api/fish-api/src/main/java/com/example/fishapi/dto/SendCodeRequest.java << EOF
package com.example.fishapi.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;

public class SendCodeRequest {

    @NotBlank(message = "Email cannot be blank")
    @Email(message = "Invalid email format")
    private String email;

    // Getter and Setter
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
}
EOF