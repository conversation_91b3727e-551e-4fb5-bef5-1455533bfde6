cat > /var/www/api/fish-api/src/main/java/com/example/fishapi/service/impl/UserServiceImpl.java << EOF
package com.example.fishapi.service.impl;

import com.example.fishapi.dto.ForgotPasswordRequest;
import com.example.fishapi.dto.ResetPasswordRequest;
import com.example.fishapi.exception.ResourceNotFoundException;
import com.example.fishapi.model.EmailVerificationCode;
import com.example.fishapi.model.User;
import com.example.fishapi.repository.EmailVerificationCodeRepository;
import com.example.fishapi.repository.UserRepository;
import com.example.fishapi.service.EmailService;
import com.example.fishapi.service.UserService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.security.authentication.BadCredentialsException;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Date;

@Service
public class UserServiceImpl implements UserService {

    private static final Logger log = LoggerFactory.getLogger(UserServiceImpl.class);
    private static final int CODE_EXPIRY_MINUTES = 1;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private EmailService emailService;

    @Autowired
    private EmailVerificationCodeRepository verificationCodeRepository;

    @Override
    @Transactional
    public void initiatePasswordReset(ForgotPasswordRequest request) {
        String email = request.getEmail();
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with email: " + email));

        if (user.getStatus() != User.UserStatus.ACTIVE) {
            log.warn("Password reset attempt for inactive user: {}", email);
            throw new BadCredentialsException("Account is not active.");
        }

        verificationCodeRepository.deleteByEmail(email);
        log.debug("Deleted existing verification codes for email: {}", email);


        String code = generateVerificationCode();
        LocalDateTime expiryTime = LocalDateTime.now().plus(CODE_EXPIRY_MINUTES, ChronoUnit.MINUTES);

        EmailVerificationCode verificationCode = new EmailVerificationCode(email, code, expiryTime);
        verificationCodeRepository.save(verificationCode);
        log.info("Generated and saved password reset code for email: {}", email);

        emailService.sendVerificationCode(email, code);
    }

    @Override
    @Transactional
    public void resetPassword(ResetPasswordRequest request) {
        String email = request.getEmail();
        String code = request.getCode();
        String newPassword = request.getNewPassword();

        EmailVerificationCode verificationRecord = verificationCodeRepository
                .findByEmailAndCodeAndExpiryTimeAfter(email, code, LocalDateTime.now())
                .orElseThrow(() -> {
                    log.warn("Invalid or expired verification code attempt for email: {}", email);
                    return new BadCredentialsException("Invalid or expired verification code.");
                });

        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with email: " + email));

        if (passwordEncoder.matches(newPassword, user.getPasswordHash())) {
             throw new IllegalArgumentException("New password cannot be the same as the old password.");
        }

        user.setPasswordHash(passwordEncoder.encode(newPassword));
        user.setUpdatedAt(new Date());
        userRepository.save(user);

        verificationCodeRepository.delete(verificationRecord);
        log.info("Password reset successfully for email: {} and verification code deleted.", email);
    }

    private String generateVerificationCode() {
        SecureRandom random = new SecureRandom();
        int num = random.nextInt(900000) + 100000;
        return String.valueOf(num);
    }

    // --- 定期清理过期验证码的任务 ---
    // 设置为每小时的第5分钟执行
    @Scheduled(cron = "0 5 * * * ?")
    @Transactional
    public void cleanupExpiredVerificationCodes() {
        LocalDateTime now = LocalDateTime.now();
        log.info("Running cleanup task for expired verification codes older than {}", now);
        int deletedCount = verificationCodeRepository.deleteByExpiryTimeBefore(now);
        if (deletedCount > 0) {
             log.info("Deleted {} expired verification codes.", deletedCount);
        } else {
             log.info("No expired verification codes found to delete.");
        }
    }
}
EOF