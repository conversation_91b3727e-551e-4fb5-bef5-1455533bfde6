cat /var/www/api/fish-api/src/main/java/com/example/fishapi/service/FileStorageService.java
package com.example.fishapi.service;

import org.springframework.web.multipart.MultipartFile;
import java.nio.file.Path;

public interface FileStorageService {
    Path storeFile(MultipartFile file, String fileName);
    
    // 添加单参数版本的方法，内部生成文件名
    default Path storeFile(MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        return storeFile(file, originalFilename != null ? originalFilename : "unknown_file");
    }
}      

cat /var/www/api/fish-api/src/main/java/com/example/fishapi/service/impl/FileStorageServiceImpl.java
    package com.example.fishapi.service.impl;

    import com.example.fishapi.service.FileStorageService;
    import jakarta.annotation.PostConstruct;
    import lombok.extern.slf4j.Slf4j;
    import org.springframework.beans.factory.annotation.Value;
    import org.springframework.stereotype.Service;
    import org.springframework.util.StringUtils;
    import org.springframework.web.multipart.MultipartFile;

    import java.io.IOException;
    import java.io.InputStream;
    import java.nio.file.Files;
    import java.nio.file.Path;
    import java.nio.file.Paths;
    import java.nio.file.StandardCopyOption;

    @Service
    @Slf4j
    public class FileStorageServiceImpl implements FileStorageService {

        @Value("${file.upload-dir:/var/www/api/uploads}") // 默认路径
        private String uploadDir;

        private Path fileStorageLocation;

        @PostConstruct
        public void init() {
            try {
                this.fileStorageLocation = Paths.get(this.uploadDir).toAbsolutePath().normalize();
                Files.createDirectories(this.fileStorageLocation);
                log.info("文件上传目录初始化成功: {}", this.fileStorageLocation);
            } catch (Exception ex) {
                log.error("无法创建文件上传目录 {}", this.uploadDir, ex);
                throw new RuntimeException("无法创建文件上传目录。", ex);
            }
        }

        @Override
        public Path storeFile(MultipartFile file, String fileName) {
            String finalFileName = StringUtils.cleanPath(fileName);
            try {
                if(finalFileName.contains("..")) {
                    throw new RuntimeException("文件名包含无效路径序列 " + finalFileName);
                }
                if (file.isEmpty()) {
                     throw new RuntimeException("无法存储空文件 " + finalFileName);
                }
                Path targetLocation = this.fileStorageLocation.resolve(finalFileName);
                try (InputStream inputStream = file.getInputStream()) {
                    Files.copy(inputStream, targetLocation, StandardCopyOption.REPLACE_EXISTING);
                }
                log.info("文件成功存储到: {}", targetLocation);
                return targetLocation;
            } catch (IOException ex) {
                log.error("存储文件 {} 失败", finalFileName, ex);
                throw new RuntimeException("存储文件 " + finalFileName + " 失败，请重试。", ex);
            }
        }
    }