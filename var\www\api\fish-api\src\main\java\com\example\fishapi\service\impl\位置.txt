var/www/api/fish-api/src/main/java/com/example/fishapi/service/impl
/var/www/api/fish-api$ sudo cat /var/www/api/fish-api/src/main/java/com/example/fishapi/model/PredictionResultDetail.java

admin@lavm-zam5kdzhoo:~$ ls -la /var/www/api/fish-api/src/main/java/
total 12
drwxrwxr-x 3 <USER> <GROUP> 4096 Mar 21 22:09 .
drwxrwxr-x 4 <USER> <GROUP> 4096 Mar 21 22:09 ..
drwxrwxr-x 3 <USER> <GROUP> 4096 Mar 21 22:09 com
admin@lavm-zam5kdzhoo:~$ ls -la /var/www/api/fish-api/src/main/java/com
total 12
drwxrwxr-x 3 <USER> <GROUP> 4096 Mar 21 22:09 .
drwxrwxr-x 3 <USER> <GROUP> 4096 Mar 21 22:09 ..
drwxrwxr-x 3 <USER> <GROUP> 4096 Mar 21 22:09 example
admin@lavm-zam5kdzhoo:~$ ls -la /var/www/api/fish-api/src/main/java/com/example
total 12
drwxrwxr-x  3 <USER> <GROUP> 4096 Mar 21 22:09 .
drwxrwxr-x  3 <USER> <GROUP> 4096 Mar 21 22:09 ..
drwxrwxr-x 15 <USER> <GROUP> 4096 Apr  9 20:01 fishapi
admin@lavm-zam5kdzhoo:~$ ls -la /var/www/api/fish-api/src/main/java/com/example/fishapi
total 64
drwxrwxr-x 15 <USER> <GROUP> 4096 Apr  9 20:01 .
drwxrwxr-x  3 <USER> <GROUP> 4096 Mar 21 22:09 ..
drwxrwxr-x  3 <USER> <GROUP> 4096 Apr  9 19:24 client
drwxrwxr-x  3 <USER> <GROUP> 4096 Apr  9 20:12 config
drwxrwxr-x  3 <USER> <GROUP> 4096 Apr  9 22:31 controller
drwxrwxr-x  4 <USER> <GROUP> 4096 Apr 21 14:21 dto
drwxr-xr-x  2 <USER>  <GROUP>  4096 Apr  9 17:41 entity
drwxr-xr-x  2 <USER>  <GROUP>  4096 Apr  6 00:12 event
drwxr-xr-x  2 <USER>  <GROUP>  4096 Apr  6 00:12 exception
-rw-rw-r--  1 <USER> <GROUP>  659 Apr  6 00:12 FishApiApplication.java
drwxrwxr-x  2 <USER> <GROUP> 4096 Apr  9 18:28 interceptor
drwxrwxr-x  2 <USER> <GROUP> 4096 Apr 21 14:21 model
drwxr-xr-x  2 <USER>  <GROUP>  4096 Apr  9 20:01 monitoring
drwxrwxr-x  2 <USER> <GROUP> 4096 Apr  9 17:41 repository
drwxrwxr-x  2 <USER> <GROUP> 4096 Apr  6 00:12 security
drwxrwxr-x  4 <USER> <GROUP> 4096 Apr 21 14:36 service
admin@lavm-zam5kdzhoo:~$ ls -la /var/www/api/fish-api/src/main/java/com/example/fishapi/controller
total 144
drwxrwxr-x  3 <USER> <GROUP>  4096 Apr  9 22:31 .
drwxrwxr-x 15 <USER> <GROUP>  4096 Apr  9 20:01 ..
-rw-rw-r--  1 <USER> <GROUP>  2224 Apr  6 00:12 AdminController.java
-rw-r--r--  1 <USER>  <GROUP>   2487 Apr  9 19:54 ApiMonitoringController.java
-rw-r--r--  1 <USER>  <GROUP>  10162 Apr  9 19:44 ApiMonitoringController.java.bak
-rw-r--r--  1 <USER>  <GROUP>  10171 Apr  9 19:51 ApiMonitoringController.java.bak2
-rw-rw-r--  1 <USER> <GROUP>  2530 Apr  7 19:25 AuthController.java
-rw-rw-r--  1 <USER> <GROUP>  2532 Apr  7 19:25 AuthController.java.bak
-rw-r--r--  1 <USER>  <GROUP>   3736 Apr  6 00:12 DirectSummaryAccessController.java
-rw-r--r--  1 <USER>  <GROUP>   4192 Apr  6 00:12 ExampleDataController.java
-rw-rw-r--  1 <USER> <GROUP>  6684 Apr  6 00:12 FileUploadController.java
-rw-rw-r--  1 <USER> <GROUP>  2390 Apr  6 00:12 FishSpeciesController.java
drwxr-xr-x  2 <USER>  <GROUP>   4096 Apr  9 22:01 monitor
-rw-rw-r--  1 <USER> <GROUP> 13913 Apr  9 22:29 PredictionController.java
-rw-r--r--  1 <USER>  <GROUP>   8343 Apr  4 21:05 PredictionController.java.backup
-rw-r--r--  1 <USER>  <GROUP>   8343 Apr  4 21:07 PredictionController.java.bak
-rw-rw-r--  1 <USER> <GROUP>  7366 Apr  3 09:31 PredictionController.javay
-rw-r--r--  1 <USER>  <GROUP>   5017 Apr  7 14:04 PredictionSummaryAccessController.java
-rw-r--r--  1 <USER>  <GROUP>    898 Apr  6 00:12 PublicApiController.java
-rw-r--r--  1 <USER>  <GROUP>    522 Apr  9 22:31 RootController.java
-rw-r--r--  1 <USER>  <GROUP>   1669 Apr  6 00:12 UserTasksController.java
admin@lavm-zam5kdzhoo:~$ ls -la /var/www/api/fish-api/src/main/java/com/example/fishapi/service
total 84
drwxrwxr-x  4 <USER> <GROUP>  4096 Apr 21 14:36 .
drwxrwxr-x 15 <USER> <GROUP>  4096 Apr  9 20:01 ..
-rw-r--r--  1 <USER>  <GROUP>   2954 Apr  6 00:12 BatchPredictionService.java
drwxr-xr-x  2 <USER>  <GROUP>   4096 Apr  6 00:12 excel
-rw-r--r--  1 <USER>  <GROUP>    397 Apr  6 13:25 ExportService.java
-rw-rw-r--  1 <USER> <GROUP>   497 Apr  6 00:12 FileStorageService.java
-rw-rw-r--  1 <USER> <GROUP>   536 Apr  6 00:12 FishSpeciesService.java
drwxrwxr-x  2 <USER> <GROUP>  4096 Apr 21 14:24 impl
-rw-r--r--  1 <USER>  <GROUP>  14302 Apr 21 14:36 PredictionAnalysisService.java
-rw-rw-r--  1 <USER> <GROUP>  2151 Apr  6 12:26 PredictionResultService.java
-rw-r--r--  1 <USER>  <GROUP>   1782 Apr  6 12:17 PredictionResultService.java.bak
-rw-r--r--  1 <USER>  <GROUP>   4268 Apr  6 00:12 PredictionSummaryService.java
-rw-rw-r--  1 <USER> <GROUP>  1841 Apr  6 00:12 PredictionTaskService.java
-rw-r--r--  1 <USER>  <GROUP>   1424 Apr  4 18:58 PredictionTaskService.java.bak
-rw-r--r--  1 <USER>  <GROUP>    938 Apr  6 00:12 ResultServiceHelper.java
-rw-rw-r--  1 <USER> <GROUP>  1623 Apr  7 19:25 UserDetailsServiceImpl.java
-rw-rw-r--  1 <USER> <GROUP>  1625 Apr  7 19:25 UserDetailsServiceImpl.java.bak
admin@lavm-zam5kdzhoo:~$ ls -la /var/www/api/fish-api/src/main/java/com/example/fishapi/service/entity
ls: cannot access '/var/www/api/fish-api/src/main/java/com/example/fishapi/service/entity': No such file or directory
admin@lavm-zam5kdzhoo:~$ ls -la /var/www/api/fish-api/src/main/java/com/example/fishapi/entity
total 16
drwxr-xr-x  2 <USER>  <GROUP>  4096 Apr  9 17:41 .
drwxrwxr-x 15 <USER> <GROUP> 4096 Apr  9 20:01 ..
-rw-r--r--  1 <USER>  <GROUP>   892 Apr  8 10:14 ApiPerformanceMetric.java
-rw-r--r--  1 <USER>  <GROUP>   759 Apr  9 17:41 SystemAlert.java
admin@lavm-zam5kdzhoo:~$ ls -la /var/www/api/fish-api/src/main/java/com/example/fishapi/repository
total 40
drwxrwxr-x  2 <USER> <GROUP> 4096 Apr  9 17:41 .
drwxrwxr-x 15 <USER> <GROUP> 4096 Apr  9 20:01 ..
-rw-r--r--  1 <USER>  <GROUP>  1306 Apr  8 10:14 ApiPerformanceMetricRepository.java
-rw-rw-r--  1 <USER> <GROUP>  492 Apr  6 00:12 FishSpeciesRepository.java
-rw-rw-r--  1 <USER> <GROUP>  995 Apr  7 14:13 PredictionResultDetailRepository.java
-rw-rw-r--  1 <USER> <GROUP>  797 Apr  7 11:31 PredictionResultRepository.java
-rw-r--r--  1 <USER>  <GROUP>   485 Apr  7 11:22 PredictionSummaryRepository.java
-rw-rw-r--  1 <USER> <GROUP> 1778 Apr  6 00:12 PredictionTaskRepository.java
-rw-r--r--  1 <USER>  <GROUP>   443 Apr  9 17:41 SystemAlertRepository.java
-rw-rw-r--  1 <USER> <GROUP>  443 Apr  6 00:12 UserRepository.java
admin@lavm-zam5kdzhoo:~$ ls -la /var/www/api/fish-api/src/main/java/com/example/fishapi/config
total 72
drwxrwxr-x  3 <USER> <GROUP> 4096 Apr  9 20:12 .
drwxrwxr-x 15 <USER> <GROUP> 4096 Apr  9 20:01 ..
drwxr-xr-x  2 <USER>  <GROUP>  4096 Apr  8 10:14 aop
-rw-r--r--  1 <USER>  <GROUP>   547 Apr  6 00:12 AppConfig.java
-rw-rw-r--  1 <USER> <GROUP> 2505 Apr  7 19:24 DataInitializer.java
-rw-rw-r--  1 <USER> <GROUP> 2693 Apr  7 19:24 DataInitializer.java.bak
-rw-r--r--  1 <USER>  <GROUP>  4808 Apr  8 21:48 ImprovedSecurityConfig.java
-rw-rw-r--  1 <USER> <GROUP>  811 Apr  9 18:31 JacksonConfig.java
-rw-r--r--  1 <USER>  <GROUP>   917 Apr  9 20:12 MonitoringSecurityConfig.java
-rw-rw-r--  1 <USER> <GROUP>  558 Apr  6 00:12 PasswordEncoderConfig.java
-rw-r--r--  1 <USER>  <GROUP>   363 Apr  9 18:29 SchedulingConfig.java
-rw-r--r--  1 <USER>  <GROUP>  4292 Apr  2 21:09 SecurityConfig.java.bak
-rw-rw-r--  1 <USER> <GROUP> 1688 Apr  8 21:29 SwaggerConfig.java
-rw-rw-r--  1 <USER> <GROUP> 1428 Apr  6 00:12 ThymeleafConfig.java
-rw-rw-r--  1 <USER> <GROUP>  776 Apr  6 00:12 WebConfig.java
-rw-r--r--  1 <USER>  <GROUP>   961 Apr  9 18:29 WebMvcConfig.java
admin@lavm-zam5kdzhoo:~$ ls -la /var/www/api/fish-api/src/main/java/com/example/fishapi/security
total 20
drwxrwxr-x  2 <USER> <GROUP> 4096 Apr  6 00:12 .
drwxrwxr-x 15 <USER> <GROUP> 4096 Apr  9 20:01 ..
-rw-rw-r--  1 <USER> <GROUP> 4073 Apr  6 00:12 JwtAuthenticationFilter.java
-rw-rw-r--  1 <USER> <GROUP> 6797 Apr  6 00:12 JwtAuthorizationFilter.java

admin@lavm-zam5kdzhoo:~$ ls -la /var/www/api/fish-api/src/main/java/com/example/fishapi/model
total 68
drwxrwxr-x  2 <USER> <GROUP> 4096 Apr 21 14:21 .
drwxrwxr-x 15 <USER> <GROUP> 4096 Apr  9 20:01 ..
-rw-rw-r--  1 <USER> <GROUP> 1760 Apr  7 19:16 FishSpecies.java
-rw-rw-r--  1 <USER> <GROUP> 1566 Apr  7 19:16 FishSpecies.java.bak
-rw-rw-r--  1 <USER> <GROUP> 6133 Apr 21 14:21 PredictionResultDetail.java
-rw-rw-r--  1 <USER> <GROUP> 1913 Apr  7 19:12 PredictionResultDetail.java.bak
-rw-r--r--  1 <USER>  <GROUP>  1977 Apr 20 18:37 PredictionResultDetail.java.bak_change_type_20250420_183742
-rw-rw-r--  1 <USER> <GROUP> 2942 Apr  7 19:13 PredictionResult.java
-rw-rw-r--  1 <USER> <GROUP> 2883 Apr  7 19:13 PredictionResult.java.bak
-rw-r--r--  1 <USER>  <GROUP>  6579 Apr 20 19:10 PredictionSummary.java
-rw-r--r--  1 <USER> <GROUP> 2513 Apr  7 19:12 PredictionSummary.java.bak
-rw-rw-r--  1 <USER> <GROUP> 3024 Apr  7 19:37 PredictionTask.java
-rw-rw-r--  1 <USER> <GROUP> 2426 Apr  7 19:14 PredictionTask.java.bak
-rw-rw-r--  1 <USER> <GROUP> 2066 Apr  7 19:22 User.java
-rw-rw-r--  1 <USER> <GROUP> 1972 Apr  7 19:22 User.java.bak

ls -la /var/www/api/fish-api/src/main/java/com/example/fishapi/client
total 36
drwxrwxr-x  3 <USER> <GROUP>  4096 Apr  9 19:24 .
drwxrwxr-x 15 <USER> <GROUP>  4096 May  1 12:56 ..
-rw-rw-r--  1 <USER> <GROUP> 14518 Apr  9 19:33 ExternalApiClient.java
-rw-rw-r--  1 <USER> <GROUP>   882 Apr  6 00:12 FlaskApiClient.java
-rw-r--r--  1 <USER>  <GROUP>   3584 Apr  6 00:12 FlaskModelApiClient.java
drwxr-xr-x  2 <USER>  <GROUP>   4096 Apr 19 23:19 impl