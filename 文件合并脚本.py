import pandas as pd
import os

# Define file paths
file1 = 'C:/Users/<USER>/Desktop/CQ.txt'
file2 = 'C:/Users/<USER>/Desktop/WQ.txt'
output_file = 'merged.csv'

# Check if files exist
if not os.path.exists(file1):
    print(f"Error: File '{file1}' not found.")
    exit()
if not os.path.exists(file2):
    print(f"Error: File '{file2}' not found.")
    exit()

# Read the two files into pandas DataFrames
# The first column 'Taxon' is used as the index
df1 = pd.read_csv(file1, sep='	', index_col='Taxon')
df2 = pd.read_csv(file2, sep='	', index_col='Taxon')

# Concatenate the two DataFrames.
# 'outer' join keeps all columns from both DataFrames.
# NaN values will be created where a sample doesn't have a value for a particular phylum.
merged_df = pd.concat([df1, df2], join='outer', sort=False)

# Fill NaN values with 0. This assumes that if a phylum is not present for a sample, its abundance is 0.
merged_df = merged_df.fillna(0)

# Reset the index to turn the 'Taxon' (now the index) back into a column.
merged_df.reset_index(inplace=True)

# Rename the 'Taxon' column to 'ID' for clarity as requested.
merged_df.rename(columns={'Taxon': 'ID'}, inplace=True)

# Save the merged DataFrame to a CSV file.
merged_df.to_csv(output_file, index=False)

print(f"Successfully merged {file1} and {file2} into {output_file}") 