admin@lavm-zam5kdzhoo:~$ cat /var/www/api/fish-api/src/main/java/com/example/fishapi/service/PredictionTaskService.java
package com.example.fishapi.service;

import java.util.List;
import com.example.fishapi.dto.PredictionTaskDto;
import com.example.fishapi.model.PredictionTask.TaskStatus; 
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

public interface PredictionTaskService {

    Page<PredictionTaskDto> getUserPredictionTasks(String username, Pageable pageable);

    PredictionTaskDto getPredictionTaskById(Long id, String username);

    PredictionTaskDto createPredictionTask(MultipartFile file, String fishType, String taskName, String username);

    // 接收 TaskStatus 枚举
    void updatePredictionTaskStatus(Long taskId, TaskStatus status);

    // 接口声明 
    void updateTaskStatusAndStoredFileName(Long taskId, TaskStatus status, String storedFileName);

    void triggerPrediction(Long taskId, String filePath, String fishType, String username);

    // 保留接收 String 的方法作为重载
    void updatePredictionTaskStatus(Long taskId, String statusString);
    void updateTaskStatusAndFilePath(Long taskId, String statusString, String filePath);
    
    void deletePredictionTask(Long taskId, String username);
    
    int deletePredictionTasks(List<Long> taskIds, String username);
    
    int deleteAllUserPredictionTasks(String username);
    
    // 为事件监听器添加特殊方法
    void updateTaskStatus(Long taskId, String status, String username);
}